#!/bin/bash

# Test script for Polar webhook integration
# Usage: ./test-webhook.sh [local|production] [created|updated|cancelled]

set -e

# Configuration
LOCAL_URL="http://localhost:54321/functions/v1/polar-webhook"
PRODUCTION_URL="https://your-project.functions.supabase.co/polar-webhook"
WEBHOOK_SECRET="your-webhook-secret-here"

# Get parameters
ENVIRONMENT=${1:-local}
EVENT_TYPE=${2:-created}

# Set URL based on environment
if [ "$ENVIRONMENT" == "production" ]; then
    URL=$PRODUCTION_URL
else
    URL=$LOCAL_URL
fi

# Set test data file
case $EVENT_TYPE in
    "created")
        DATA_FILE="test-data/subscription-created.json"
        ;;
    "updated")
        DATA_FILE="test-data/subscription-updated.json"
        ;;
    "cancelled")
        DATA_FILE="test-data/subscription-cancelled.json"
        ;;
    *)
        echo "Invalid event type. Use: created, updated, or cancelled"
        exit 1
        ;;
esac

# Check if data file exists
if [ ! -f "$DATA_FILE" ]; then
    echo "Error: Test data file $DATA_FILE not found"
    exit 1
fi

# Read the JSON data
JSON_DATA=$(cat "$DATA_FILE")

# Generate HMAC signature
SIGNATURE=$(echo -n "$JSON_DATA" | openssl dgst -sha256 -hmac "$WEBHOOK_SECRET" | cut -d' ' -f2)

echo "Testing webhook with:"
echo "URL: $URL"
echo "Event: $EVENT_TYPE"
echo "Data file: $DATA_FILE"
echo "Signature: sha256=$SIGNATURE"
echo ""

# Send the webhook request
curl -X POST "$URL" \
  -H "Content-Type: application/json" \
  -H "polar-webhook-signature: sha256=$SIGNATURE" \
  -d "$JSON_DATA" \
  -v

echo ""
echo "Test completed!"
