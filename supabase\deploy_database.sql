-- M-Duka Complete Database Deployment Script
-- Run this script in your Supabase SQL Editor to set up the complete database

-- =============================================
-- STEP 1: ENABLE EXTENSIONS
-- =============================================

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- STEP 2: CREATE CUSTOM TYPES
-- =============================================

DO $$ BEGIN
    CREATE TYPE subscription_status AS ENUM ('active', 'cancelled', 'expired', 'trial', 'pending');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE billing_cycle AS ENUM ('monthly', 'yearly');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE order_status AS ENUM ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =============================================
-- STEP 3: CREATE TABLES
-- =============================================

-- Subscription Plans Table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price_monthly DECIMAL(10,2) NOT NULL DEFAULT 0,
    price_yearly DECIMAL(10,2),
    max_products INTEGER DEFAULT -1,
    max_orders INTEGER DEFAULT -1,
    max_storage_gb INTEGER DEFAULT 1,
    features JSONB DEFAULT '[]'::jsonb,
    pesapal_product_code VARCHAR(100),
    pesapal_product_url TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Subscriptions Table
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    plan_id UUID NOT NULL REFERENCES subscription_plans(id),
    status subscription_status DEFAULT 'trial',
    current_period_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    current_period_end TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '30 days',
    trial_end TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '14 days',
    billing_cycle billing_cycle DEFAULT 'monthly',
    payment_method VARCHAR(50),
    pesapal_order_id VARCHAR(100),
    pesapal_tracking_id VARCHAR(100),
    pesapal_merchant_reference VARCHAR(100),
    pesapal_product_code VARCHAR(100),
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Stores Table
CREATE TABLE IF NOT EXISTS stores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    logo_url TEXT,
    banner_url TEXT,
    whatsapp_number VARCHAR(20),
    email VARCHAR(255),
    phone VARCHAR(20),
    website_url TEXT,
    business_hours JSONB DEFAULT '{}'::jsonb,
    payment_instructions TEXT,
    shipping_info TEXT,
    return_policy TEXT,
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Tanzania',
    timezone VARCHAR(50) DEFAULT 'Africa/Dar_es_Salaam',
    currency VARCHAR(3) DEFAULT 'TZS',
    language VARCHAR(5) DEFAULT 'en',
    theme_color VARCHAR(7) DEFAULT '#667eea',
    is_active BOOLEAN DEFAULT true,
    is_public BOOLEAN DEFAULT true,
    meta_title VARCHAR(255),
    meta_description TEXT,
    view_count INTEGER DEFAULT 0,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Product Categories Table
CREATE TABLE IF NOT EXISTS product_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(store_id, name)
);

-- Products Table
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
    category_id UUID REFERENCES product_categories(id) ON DELETE SET NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    sku VARCHAR(100),
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    compare_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    track_inventory BOOLEAN DEFAULT false,
    inventory_quantity INTEGER DEFAULT 0,
    low_stock_threshold INTEGER DEFAULT 5,
    images JSONB DEFAULT '[]'::jsonb,
    featured_image TEXT,
    weight DECIMAL(8,2),
    dimensions JSONB DEFAULT '{}'::jsonb,
    tags TEXT[] DEFAULT '{}',
    is_available BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    is_digital BOOLEAN DEFAULT false,
    meta_title VARCHAR(255),
    meta_description TEXT,
    view_count INTEGER DEFAULT 0,
    order_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customers Table
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    email VARCHAR(255),
    phone VARCHAR(20) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    region VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Tanzania',
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    average_order_value DECIMAL(10,2) DEFAULT 0,
    preferred_language VARCHAR(5) DEFAULT 'en',
    marketing_consent BOOLEAN DEFAULT false,
    first_order_date TIMESTAMP WITH TIME ZONE,
    last_order_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(store_id, phone)
);

-- Orders Table
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    customer_email VARCHAR(255),
    shipping_address TEXT,
    shipping_city VARCHAR(100),
    shipping_region VARCHAR(100),
    shipping_postal_code VARCHAR(20),
    shipping_country VARCHAR(100) DEFAULT 'Tanzania',
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    status order_status DEFAULT 'pending',
    payment_status payment_status DEFAULT 'pending',
    notes TEXT,
    internal_notes TEXT,
    tracking_number VARCHAR(100),
    order_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE,
    shipped_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order Items Table
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    product_name VARCHAR(255) NOT NULL,
    product_sku VARCHAR(100),
    product_image TEXT,
    unit_price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    total_price DECIMAL(10,2) NOT NULL,
    product_attributes JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Webhook Events Table
CREATE TABLE IF NOT EXISTS webhook_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(100) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    event_id VARCHAR(255),
    user_id UUID REFERENCES auth.users(id),
    subscription_id UUID REFERENCES user_subscriptions(id),
    order_id UUID REFERENCES orders(id),
    payload JSONB NOT NULL,
    processed BOOLEAN DEFAULT false,
    processed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics Events Table
CREATE TABLE IF NOT EXISTS analytics_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL,
    properties JSONB DEFAULT '{}'::jsonb,
    user_agent TEXT,
    ip_address INET,
    referrer TEXT,
    session_id VARCHAR(255),
    user_id UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- STEP 4: CREATE INDEXES
-- =============================================

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_stores_user_id ON stores(user_id);
CREATE INDEX IF NOT EXISTS idx_stores_slug ON stores(slug);
CREATE INDEX IF NOT EXISTS idx_stores_is_active ON stores(is_active);
CREATE INDEX IF NOT EXISTS idx_products_store_id ON products(store_id);
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_is_available ON products(is_available);
CREATE INDEX IF NOT EXISTS idx_orders_store_id ON orders(store_id);
CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_customers_store_id ON customers(store_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);

-- =============================================
-- STEP 5: ENABLE ROW LEVEL SECURITY
-- =============================================

ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;

-- =============================================
-- STEP 6: CREATE RLS POLICIES
-- =============================================

-- Subscription Plans Policies
CREATE POLICY "subscription_plans_select_public" ON subscription_plans
    FOR SELECT USING (is_active = true);

-- User Subscriptions Policies
CREATE POLICY "user_subscriptions_select_own" ON user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "user_subscriptions_insert_own" ON user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "user_subscriptions_update_own" ON user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- Stores Policies
CREATE POLICY "stores_select_own" ON stores
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "stores_select_public" ON stores
    FOR SELECT USING (is_public = true AND is_active = true);

CREATE POLICY "stores_insert_own" ON stores
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "stores_update_own" ON stores
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "stores_delete_own" ON stores
    FOR DELETE USING (auth.uid() = user_id);

-- Products Policies
CREATE POLICY "products_select_own" ON products
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stores
            WHERE stores.id = products.store_id
            AND stores.user_id = auth.uid()
        )
    );

CREATE POLICY "products_select_public" ON products
    FOR SELECT USING (
        is_available = true AND
        EXISTS (
            SELECT 1 FROM stores
            WHERE stores.id = products.store_id
            AND stores.is_public = true
            AND stores.is_active = true
        )
    );

CREATE POLICY "products_manage_own" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM stores
            WHERE stores.id = products.store_id
            AND stores.user_id = auth.uid()
        )
    );

-- Orders Policies
CREATE POLICY "orders_select_own" ON orders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stores
            WHERE stores.id = orders.store_id
            AND stores.user_id = auth.uid()
        )
    );

CREATE POLICY "orders_insert_public" ON orders
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM stores
            WHERE stores.id = orders.store_id
            AND stores.is_public = true
            AND stores.is_active = true
        )
    );

CREATE POLICY "orders_update_own" ON orders
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM stores
            WHERE stores.id = orders.store_id
            AND stores.user_id = auth.uid()
        )
    );

-- =============================================
-- STEP 7: INSERT SEED DATA
-- =============================================

-- Insert subscription plans
INSERT INTO subscription_plans (
    name, description, price_monthly, price_yearly, max_products, max_orders, max_storage_gb, features, sort_order
) VALUES
(
    'Free Trial',
    'Perfect for getting started with your online store',
    0.00, 0.00, 10, 50, 1,
    '["Basic store setup", "Up to 10 products", "50 orders per month", "WhatsApp integration", "Basic analytics"]'::jsonb,
    1
),
(
    'Starter',
    'Great for small businesses just starting out',
    15000.00, 150000.00, 100, 500, 5,
    '["Everything in Free", "Up to 100 products", "500 orders per month", "Custom domain", "Advanced analytics", "Email support"]'::jsonb,
    2
),
(
    'Professional',
    'Perfect for growing businesses',
    35000.00, 350000.00, 500, 2000, 20,
    '["Everything in Starter", "Up to 500 products", "2000 orders per month", "Priority support", "Advanced integrations", "Custom branding"]'::jsonb,
    3
),
(
    'Business',
    'For established businesses with high volume',
    75000.00, 750000.00, -1, -1, 100,
    '["Everything in Professional", "Unlimited products", "Unlimited orders", "Dedicated support", "API access", "White-label solution"]'::jsonb,
    4
)
ON CONFLICT DO NOTHING;
