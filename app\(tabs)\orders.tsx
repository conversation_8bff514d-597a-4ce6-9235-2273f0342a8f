import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { supabase, Order } from '../../lib/supabase';

export default function Orders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'confirmed' | 'delivered' | 'cancelled'>('all');

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get store info
      const { data: store } = await supabase
        .from('stores')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (!store) return;

      // Get orders
      const { data: ordersData, error } = await supabase
        .from('orders')
        .select('*')
        .eq('store_id', store.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading orders:', error);
        return;
      }

      setOrders(ordersData || []);
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (orderId: string, status: Order['status']) => {
    const { error } = await supabase
      .from('orders')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', orderId);

    if (error) {
      Alert.alert('Error', 'Failed to update order status');
    } else {
      loadOrders();
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending': return 'bg-orange-100 text-orange-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredOrders = filter === 'all' 
    ? orders 
    : orders.filter(order => order.status === filter);

  const OrderCard = ({ order }: { order: Order }) => (
    <View className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 mb-4">
      <View className="flex-row items-center justify-between mb-3">
        <Text className="font-semibold text-gray-900">Order #{order.id.slice(-8)}</Text>
        <View className={`px-2 py-1 rounded-full ${getStatusColor(order.status)}`}>
          <Text className="text-xs font-medium capitalize">{order.status}</Text>
        </View>
      </View>

      <View className="space-y-2">
        <View className="flex-row">
          <Text className="text-gray-600 w-20">Customer:</Text>
          <Text className="text-gray-900 flex-1">{order.customer_name}</Text>
        </View>
        <View className="flex-row">
          <Text className="text-gray-600 w-20">Phone:</Text>
          <Text className="text-gray-900 flex-1">{order.customer_phone}</Text>
        </View>
        <View className="flex-row">
          <Text className="text-gray-600 w-20">Total:</Text>
          <Text className="text-blue-600 font-bold flex-1">KSh {order.total_amount.toLocaleString()}</Text>
        </View>
        <View className="flex-row">
          <Text className="text-gray-600 w-20">Date:</Text>
          <Text className="text-gray-900 flex-1">
            {new Date(order.created_at).toLocaleDateString()}
          </Text>
        </View>
      </View>

      {order.items && order.items.length > 0 && (
        <View className="mt-3 pt-3 border-t border-gray-100">
          <Text className="text-gray-600 text-sm mb-2">Items:</Text>
          {order.items.map((item, index) => (
            <Text key={index} className="text-gray-900 text-sm">
              {item.quantity}x {item.product_name} - KSh {item.price.toLocaleString()}
            </Text>
          ))}
        </View>
      )}

      {order.notes && (
        <View className="mt-3 pt-3 border-t border-gray-100">
          <Text className="text-gray-600 text-sm">Notes:</Text>
          <Text className="text-gray-900 text-sm mt-1">{order.notes}</Text>
        </View>
      )}

      {order.status === 'pending' && (
        <View className="flex-row space-x-2 mt-4">
          <TouchableOpacity
            className="flex-1 bg-blue-600 rounded-lg py-2"
            onPress={() => updateOrderStatus(order.id, 'confirmed')}
          >
            <Text className="text-white text-center font-medium">Confirm</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="flex-1 bg-red-600 rounded-lg py-2"
            onPress={() => updateOrderStatus(order.id, 'cancelled')}
          >
            <Text className="text-white text-center font-medium">Cancel</Text>
          </TouchableOpacity>
        </View>
      )}

      {order.status === 'confirmed' && (
        <TouchableOpacity
          className="bg-green-600 rounded-lg py-2 mt-4"
          onPress={() => updateOrderStatus(order.id, 'delivered')}
        >
          <Text className="text-white text-center font-medium">Mark as Delivered</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const FilterButton = ({ status, label }: { status: typeof filter; label: string }) => (
    <TouchableOpacity
      className={`px-4 py-2 rounded-full mr-2 ${
        filter === status ? 'bg-blue-600' : 'bg-gray-200'
      }`}
      onPress={() => setFilter(status)}
    >
      <Text className={`text-sm font-medium ${
        filter === status ? 'text-white' : 'text-gray-700'
      }`}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="flex-1">
        <View className="px-6 py-4 bg-white border-b border-gray-200">
          <Text className="text-2xl font-bold text-gray-900 mb-4">Orders</Text>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row">
              <FilterButton status="all" label="All" />
              <FilterButton status="pending" label="Pending" />
              <FilterButton status="confirmed" label="Confirmed" />
              <FilterButton status="delivered" label="Delivered" />
              <FilterButton status="cancelled" label="Cancelled" />
            </View>
          </ScrollView>
        </View>

        {loading ? (
          <View className="flex-1 justify-center items-center">
            <Text className="text-gray-600">Loading orders...</Text>
          </View>
        ) : filteredOrders.length === 0 ? (
          <View className="flex-1 justify-center items-center px-6">
            <Ionicons name="receipt-outline" size={64} color="#9ca3af" />
            <Text className="text-xl font-semibold text-gray-900 mt-4">No Orders Yet</Text>
            <Text className="text-gray-600 text-center mt-2">
              Orders will appear here when customers place them
            </Text>
          </View>
        ) : (
          <ScrollView className="flex-1 px-6 py-4">
            {filteredOrders.map((order) => (
              <OrderCard key={order.id} order={order} />
            ))}
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  );
}
