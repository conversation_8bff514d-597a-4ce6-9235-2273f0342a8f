import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// Check if Supabase is properly configured
export const isSupabaseConfigured = () => {
  return supabaseUrl !== 'https://placeholder.supabase.co' &&
         supabaseAnonKey !== 'placeholder-key' &&
         supabaseUrl.includes('supabase.co');
};

// Test Supabase connection
export const testSupabaseConnection = async () => {
  try {
    console.log('Testing Supabase connection...');
    console.log('URL:', supabaseUrl);
    console.log('Key configured:', supabaseAnonKey ? 'Yes' : 'No');

    // Test basic connection by checking auth status
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Supabase connection test failed:', error);
      return { success: false, error: error.message };
    }

    console.log('Supabase connection test successful');
    return { success: true, session: data.session };
  } catch (error: any) {
    console.error('Supabase connection test error:', error);
    return { success: false, error: error.message || 'Unknown connection error' };
  }
};

// Create a mock storage for SSR
const createMockStorage = () => ({
  getItem: async () => null,
  setItem: async () => {},
  removeItem: async () => {},
});

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: Platform.OS === 'web' && typeof window === 'undefined'
      ? createMockStorage()
      : AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types
export interface Store {
  id: string;
  user_id: string;
  name: string;
  slug: string;
  description?: string;
  logo_url?: string;
  whatsapp_number?: string;
  payment_instructions?: string;
  business_hours?: string;
  subscription_id?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  store_id: string;
  name: string;
  description?: string;
  price: number;
  image_url?: string;
  category?: string;
  is_available: boolean;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  store_id: string;
  customer_name: string;
  customer_phone: string;
  items: OrderItem[];
  total_amount: number;
  status: 'pending' | 'confirmed' | 'delivered' | 'cancelled';
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  product_id: string;
  product_name: string;
  quantity: number;
  price: number;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price_monthly: number;
  price_yearly?: number;
  max_products?: number;
  max_orders?: number;
  max_storage_gb?: number;
  features: string[];
  pesapal_product_code: string;
  pesapal_product_url: string;
  is_active: boolean;
  created_at: string;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: 'active' | 'cancelled' | 'expired' | 'trial' | 'pending';
  current_period_start: string;
  current_period_end: string;
  trial_end?: string;
  payment_method?: string;
  pesapal_order_id?: string;
  pesapal_tracking_id?: string;
  pesapal_merchant_reference?: string;
  pesapal_product_code?: string;
  billing_cycle: 'monthly' | 'yearly';
  created_at: string;
  updated_at: string;
  plan?: SubscriptionPlan;
  subscription_plans?: SubscriptionPlan;
}
