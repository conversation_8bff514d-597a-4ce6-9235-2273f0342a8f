import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { supabase, Product } from '../../lib/supabase';

export default function Products() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get store info
      const { data: store } = await supabase
        .from('stores')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (!store) return;

      // Get products
      const { data: productsData, error } = await supabase
        .from('products')
        .select('*')
        .eq('store_id', store.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading products:', error);
        return;
      }

      setProducts(productsData || []);
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteProduct = async (productId: string) => {
    Alert.alert(
      'Delete Product',
      'Are you sure you want to delete this product?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const { error } = await supabase
              .from('products')
              .delete()
              .eq('id', productId);

            if (error) {
              Alert.alert('Error', 'Failed to delete product');
            } else {
              loadProducts();
            }
          },
        },
      ]
    );
  };

  const ProductCard = ({ product }: { product: Product }) => (
    <View className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 mb-4">
      <View className="flex-row">
        {product.image_url ? (
          <Image
            source={{ uri: product.image_url }}
            className="w-16 h-16 rounded-lg mr-4"
            resizeMode="cover"
          />
        ) : (
          <View className="w-16 h-16 bg-gray-200 rounded-lg mr-4 items-center justify-center">
            <Ionicons name="image" size={24} color="#9ca3af" />
          </View>
        )}
        
        <View className="flex-1">
          <Text className="font-semibold text-gray-900 text-lg">{product.name}</Text>
          {product.description && (
            <Text className="text-gray-600 text-sm mt-1" numberOfLines={2}>
              {product.description}
            </Text>
          )}
          <Text className="text-blue-600 font-bold text-lg mt-2">
            KSh {product.price.toLocaleString()}
          </Text>
          {product.category && (
            <Text className="text-gray-500 text-xs mt-1">{product.category}</Text>
          )}
        </View>
        
        <View className="ml-2">
          <TouchableOpacity
            className="p-2"
            onPress={() => router.push(`/products/edit/${product.id}`)}
          >
            <Ionicons name="pencil" size={20} color="#6b7280" />
          </TouchableOpacity>
          <TouchableOpacity
            className="p-2"
            onPress={() => deleteProduct(product.id)}
          >
            <Ionicons name="trash" size={20} color="#ef4444" />
          </TouchableOpacity>
        </View>
      </View>
      
      <View className="flex-row items-center mt-3">
        <View className={`w-2 h-2 rounded-full mr-2 ${product.is_available ? 'bg-green-500' : 'bg-red-500'}`} />
        <Text className={`text-sm ${product.is_available ? 'text-green-600' : 'text-red-600'}`}>
          {product.is_available ? 'Available' : 'Out of Stock'}
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="flex-1">
        <View className="px-6 py-4 bg-white border-b border-gray-200">
          <View className="flex-row items-center justify-between">
            <Text className="text-2xl font-bold text-gray-900">Products</Text>
            <TouchableOpacity
              className="bg-blue-600 rounded-lg px-4 py-2 flex-row items-center"
              onPress={() => router.push('/products/add')}
            >
              <Ionicons name="add" size={20} color="white" />
              <Text className="text-white font-semibold ml-1">Add</Text>
            </TouchableOpacity>
          </View>
        </View>

        {loading ? (
          <View className="flex-1 justify-center items-center">
            <Text className="text-gray-600">Loading products...</Text>
          </View>
        ) : products.length === 0 ? (
          <View className="flex-1 justify-center items-center px-6">
            <Ionicons name="cube-outline" size={64} color="#9ca3af" />
            <Text className="text-xl font-semibold text-gray-900 mt-4">No Products Yet</Text>
            <Text className="text-gray-600 text-center mt-2">
              Start by adding your first product to your store
            </Text>
            <TouchableOpacity
              className="bg-blue-600 rounded-lg px-6 py-3 mt-6"
              onPress={() => router.push('/products/add')}
            >
              <Text className="text-white font-semibold">Add Your First Product</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <ScrollView className="flex-1 px-6 py-4">
            {products.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  );
}
