import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function Discounts() {
  return (
    <View style={styles.container}>
      <View style={styles.pageHeader}>
        <Text style={styles.pageTitle}>Discounts</Text>
        <Text style={styles.pageSubtitle}>Create and manage discount codes and promotions.</Text>
      </View>
      <View style={styles.content}>
        <View style={styles.premiumCard}>
          <View style={styles.iconContainer}>
            <Ionicons name="pricetag-outline" size={64} color="#8b5cf6" />
          </View>
          <Text style={styles.premiumTitle}>Discount Management</Text>
          <Text style={styles.premiumText}>
            Create percentage discounts, fixed amount coupons, and promotional campaigns 
            to boost your sales and attract more customers.
          </Text>
          <View style={styles.badge}>
            <Text style={styles.badgeText}>PREMIUM</Text>
          </View>
          <TouchableOpacity style={styles.upgradeButton}>
            <Text style={styles.upgradeButtonText}>Upgrade to Premium</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f9fafb' },
  pageHeader: { backgroundColor: 'white', paddingHorizontal: 24, paddingVertical: 20, borderBottomWidth: 1, borderBottomColor: '#e5e7eb' },
  pageTitle: { fontSize: 24, fontWeight: '700', color: '#1f2937', marginBottom: 4 },
  pageSubtitle: { fontSize: 14, color: '#6b7280' },
  content: { flex: 1, padding: 24 },
  premiumCard: { backgroundColor: 'white', borderRadius: 12, padding: 32, alignItems: 'center', shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 8, elevation: 4, borderWidth: 1, borderColor: '#f3f4f6' },
  iconContainer: { marginBottom: 24 },
  premiumTitle: { fontSize: 20, fontWeight: '600', color: '#1f2937', marginBottom: 12, textAlign: 'center' },
  premiumText: { fontSize: 14, color: '#6b7280', textAlign: 'center', lineHeight: 20, marginBottom: 24 },
  badge: { backgroundColor: '#8b5cf6', paddingHorizontal: 12, paddingVertical: 6, borderRadius: 6, marginBottom: 24 },
  badgeText: { fontSize: 12, fontWeight: '600', color: 'white' },
  upgradeButton: { backgroundColor: '#8b5cf6', paddingHorizontal: 24, paddingVertical: 12, borderRadius: 8 },
  upgradeButtonText: { fontSize: 14, fontWeight: '600', color: 'white' },
});
