import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import ConfigurationCheck from '../components/ConfigurationCheck';
import ErrorBoundary from '../components/ErrorBoundary';
import '../global.css';

export default function RootLayout() {
  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <PaperProvider>
          <ConfigurationCheck>
            <Stack>
              <Stack.Screen name="index" options={{ headerShown: false }} />
              <Stack.Screen name="splash" options={{ headerShown: false }} />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="auth" options={{ headerShown: false }} />
              <Stack.Screen name="onboarding" options={{ headerShown: false }} />
              <Stack.Screen name="organization-setup" options={{ headerShown: false }} />
              <Stack.Screen name="store/[storeId]" options={{ headerShown: false }} />
              <Stack.Screen name="products/add" options={{ headerShown: false }} />
              <Stack.Screen name="products/edit/[id]" options={{ headerShown: false }} />
              <Stack.Screen name="settings/store-info" options={{ headerShown: false }} />
              <Stack.Screen name="settings/whatsapp" options={{ headerShown: false }} />
            </Stack>
          </ConfigurationCheck>
          <StatusBar style="auto" />
        </PaperProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}
