# Polar Webhook Deployment Commands
# Run these commands to deploy the webhook integration

# 1. Apply database schema changes
Write-Host "Applying database schema changes..." -ForegroundColor Yellow
# Run this in your Supabase SQL editor or via psql:
# psql -h [your-db-host] -U postgres -d postgres -f supabase-polar-schema.sql

# 2. Install Supabase CLI (if not already installed)
if (-not (Get-Command supabase -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Supabase CLI..." -ForegroundColor Yellow
    npm install -g supabase
}

# 3. Login to Supabase (if not already logged in)
Write-Host "Logging in to Supabase..." -ForegroundColor Yellow
supabase login

# 4. Link to your project (replace with your project reference)
Write-Host "Linking to your Supabase project..." -ForegroundColor Yellow
# supabase link --project-ref your-project-ref

# 5. Deploy the Edge Function
Write-Host "Deploying Polar webhook Edge Function..." -ForegroundColor Yellow
supabase functions deploy polar-webhook

# 6. Set environment variables (run these in your Supabase dashboard or via CLI)
Write-Host "Setting environment variables..." -ForegroundColor Yellow
Write-Host "Please set these in your Supabase project settings > Edge Functions:" -ForegroundColor Cyan
Write-Host "- POLAR_WEBHOOK_SECRET: Your webhook secret from Polar"
Write-Host "- SUPABASE_URL: Your Supabase project URL"
Write-Host "- SUPABASE_SERVICE_ROLE_KEY: Your service role key"

# Example commands (replace with your actual values):
# supabase secrets set POLAR_WEBHOOK_SECRET=your-secret-here
# supabase secrets set SUPABASE_URL=https://your-project.supabase.co
# supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your-service-key-here

Write-Host "`nDeployment completed!" -ForegroundColor Green
Write-Host "Your webhook URL is: https://your-project.functions.supabase.co/polar-webhook" -ForegroundColor Green
Write-Host "Configure this URL in your Polar dashboard webhook settings." -ForegroundColor Green
