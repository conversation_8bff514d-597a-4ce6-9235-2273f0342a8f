-- Seed Data for M-Duka
-- This file contains initial data for the application

-- =============================================
-- SUBSCRIPTION PLANS
-- =============================================

INSERT INTO subscription_plans (
    id,
    name,
    description,
    price_monthly,
    price_yearly,
    max_products,
    max_orders,
    max_storage_gb,
    features,
    pesapal_product_code,
    is_active,
    sort_order
) VALUES 
(
    uuid_generate_v4(),
    'Free Trial',
    'Perfect for getting started with your online store',
    0.00,
    0.00,
    10,
    50,
    1,
    '["Basic store setup", "Up to 10 products", "50 orders per month", "WhatsApp integration", "Basic analytics"]'::jsonb,
    'MDUKA_TRIAL',
    true,
    1
),
(
    uuid_generate_v4(),
    'Starter',
    'Great for small businesses just starting out',
    15000.00,
    150000.00,
    100,
    500,
    5,
    '["Everything in Free", "Up to 100 products", "500 orders per month", "Custom domain", "Advanced analytics", "Email support"]'::jsonb,
    'MDUKA_STARTER',
    true,
    2
),
(
    uuid_generate_v4(),
    'Professional',
    'Perfect for growing businesses',
    35000.00,
    350000.00,
    500,
    2000,
    20,
    '["Everything in Starter", "Up to 500 products", "2000 orders per month", "Priority support", "Advanced integrations", "Custom branding"]'::jsonb,
    'MDUKA_PRO',
    true,
    3
),
(
    uuid_generate_v4(),
    'Business',
    'For established businesses with high volume',
    75000.00,
    750000.00,
    -1,
    -1,
    100,
    '["Everything in Professional", "Unlimited products", "Unlimited orders", "Dedicated support", "API access", "White-label solution"]'::jsonb,
    'MDUKA_BUSINESS',
    true,
    4
);

-- =============================================
-- DEMO STORE DATA (Optional)
-- =============================================

-- Note: This would typically be created when a user signs up
-- This is just for demonstration purposes

-- Demo user would be created through Supabase Auth
-- INSERT INTO auth.users would be handled by Supabase Auth system

-- Demo store (this would be created after user signup)
/*
INSERT INTO stores (
    id,
    user_id,
    name,
    slug,
    description,
    whatsapp_number,
    business_hours,
    payment_instructions,
    currency,
    theme_color,
    is_public,
    is_active
) VALUES (
    uuid_generate_v4(),
    'demo-user-id', -- This would be a real user ID from auth.users
    'Demo Electronics Store',
    'demo-electronics',
    'Your one-stop shop for quality electronics and gadgets',
    '+************',
    '{"monday": "9:00-18:00", "tuesday": "9:00-18:00", "wednesday": "9:00-18:00", "thursday": "9:00-18:00", "friday": "9:00-18:00", "saturday": "9:00-15:00", "sunday": "closed"}'::jsonb,
    'We accept mobile money (M-Pesa, Tigo Pesa, Airtel Money) and cash on delivery.',
    'TZS',
    '#667eea',
    true,
    true
);
*/

-- =============================================
-- SYSTEM CONFIGURATION
-- =============================================

-- Create a system configuration table for app-wide settings
CREATE TABLE IF NOT EXISTS system_config (
    key VARCHAR(100) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default system configuration
INSERT INTO system_config (key, value, description) VALUES
('app_version', '"1.0.0"'::jsonb, 'Current application version'),
('maintenance_mode', 'false'::jsonb, 'Whether the app is in maintenance mode'),
('max_file_size_mb', '10'::jsonb, 'Maximum file upload size in MB'),
('supported_currencies', '["TZS", "USD", "KES", "UGX"]'::jsonb, 'List of supported currencies'),
('supported_languages', '["en", "sw"]'::jsonb, 'List of supported languages'),
('default_country', '"Tanzania"'::jsonb, 'Default country for new stores'),
('default_timezone', '"Africa/Dar_es_Salaam"'::jsonb, 'Default timezone'),
('whatsapp_api_enabled', 'true'::jsonb, 'Whether WhatsApp API integration is enabled'),
('analytics_enabled', 'true'::jsonb, 'Whether analytics tracking is enabled'),
('email_notifications_enabled', 'true'::jsonb, 'Whether email notifications are enabled'),
('sms_notifications_enabled', 'false'::jsonb, 'Whether SMS notifications are enabled'),
('payment_methods', '["mobile_money", "cash_on_delivery", "bank_transfer"]'::jsonb, 'Available payment methods'),
('trial_period_days', '14'::jsonb, 'Trial period duration in days'),
('max_stores_per_user', '1'::jsonb, 'Maximum number of stores per user'),
('featured_stores_limit', '10'::jsonb, 'Number of stores to feature on homepage');

-- =============================================
-- PRODUCT CATEGORIES (Common categories)
-- =============================================

-- Create a reference table for common product categories
CREATE TABLE IF NOT EXISTS category_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert common product categories
INSERT INTO category_templates (name, description, icon, sort_order) VALUES
('Electronics', 'Phones, computers, gadgets and electronic devices', 'smartphone', 1),
('Fashion & Clothing', 'Clothes, shoes, accessories and fashion items', 'shirt', 2),
('Home & Garden', 'Furniture, home decor, kitchen items and garden supplies', 'home', 3),
('Health & Beauty', 'Cosmetics, skincare, health supplements and beauty products', 'heart', 4),
('Sports & Fitness', 'Sports equipment, fitness gear and outdoor activities', 'dumbbell', 5),
('Books & Education', 'Books, educational materials and learning resources', 'book', 6),
('Food & Beverages', 'Food items, drinks and culinary products', 'restaurant', 7),
('Automotive', 'Car parts, accessories and automotive services', 'car', 8),
('Baby & Kids', 'Baby products, toys and children items', 'baby', 9),
('Arts & Crafts', 'Art supplies, handmade items and craft materials', 'palette', 10),
('Music & Entertainment', 'Musical instruments, games and entertainment products', 'musical-note', 11),
('Office & Business', 'Office supplies, business equipment and stationery', 'briefcase', 12),
('Pet Supplies', 'Pet food, toys and animal care products', 'paw', 13),
('Travel & Luggage', 'Travel accessories, luggage and tourism services', 'airplane', 14),
('Jewelry & Accessories', 'Jewelry, watches and fashion accessories', 'diamond', 15);

-- =============================================
-- ANALYTICS EVENTS TEMPLATES
-- =============================================

-- Create templates for common analytics events
CREATE TABLE IF NOT EXISTS analytics_event_templates (
    event_type VARCHAR(100) PRIMARY KEY,
    description TEXT,
    properties_schema JSONB,
    is_active BOOLEAN DEFAULT true
);

INSERT INTO analytics_event_templates (event_type, description, properties_schema) VALUES
('page_view', 'User viewed a page', '{"page": "string", "referrer": "string"}'::jsonb),
('product_view', 'User viewed a product', '{"product_id": "uuid", "product_name": "string", "price": "number"}'::jsonb),
('store_view', 'User viewed a store', '{"store_id": "uuid", "store_name": "string"}'::jsonb),
('order_created', 'New order was created', '{"order_id": "uuid", "total_amount": "number", "items_count": "number"}'::jsonb),
('user_signup', 'New user registered', '{"user_id": "uuid", "signup_method": "string"}'::jsonb),
('store_created', 'New store was created', '{"store_id": "uuid", "store_name": "string", "category": "string"}'::jsonb),
('product_added', 'Product was added to store', '{"product_id": "uuid", "store_id": "uuid", "category": "string"}'::jsonb),
('whatsapp_click', 'User clicked WhatsApp button', '{"store_id": "uuid", "product_id": "uuid"}'::jsonb),
('search_performed', 'User performed a search', '{"query": "string", "results_count": "number"}'::jsonb),
('subscription_upgraded', 'User upgraded subscription', '{"plan_name": "string", "price": "number", "billing_cycle": "string"}'::jsonb);

-- =============================================
-- PERMISSIONS AND ROLES
-- =============================================

-- Enable RLS on new tables
ALTER TABLE system_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE category_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_event_templates ENABLE ROW LEVEL SECURITY;

-- System config policies (read-only for authenticated users)
CREATE POLICY "system_config_select_authenticated" ON system_config
    FOR SELECT USING (auth.role() = 'authenticated');

-- Category templates policies (read-only for everyone)
CREATE POLICY "category_templates_select_public" ON category_templates
    FOR SELECT USING (is_active = true);

-- Analytics event templates policies (read-only for authenticated users)
CREATE POLICY "analytics_event_templates_select_authenticated" ON analytics_event_templates
    FOR SELECT USING (auth.role() = 'authenticated');

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(key);
CREATE INDEX IF NOT EXISTS idx_category_templates_active ON category_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_category_templates_sort ON category_templates(sort_order);
CREATE INDEX IF NOT EXISTS idx_analytics_event_templates_active ON analytics_event_templates(is_active);

-- =============================================
-- VIEWS FOR COMMON QUERIES
-- =============================================

-- View for active subscription plans with pricing
CREATE OR REPLACE VIEW active_subscription_plans AS
SELECT 
    id,
    name,
    description,
    price_monthly,
    price_yearly,
    max_products,
    max_orders,
    max_storage_gb,
    features,
    sort_order,
    CASE 
        WHEN price_yearly IS NOT NULL AND price_yearly > 0 
        THEN ROUND((price_monthly * 12 - price_yearly) / (price_monthly * 12) * 100, 0)
        ELSE 0 
    END as yearly_discount_percent
FROM subscription_plans
WHERE is_active = true
ORDER BY sort_order;

-- View for store statistics
CREATE OR REPLACE VIEW store_statistics AS
SELECT 
    s.id,
    s.name,
    s.user_id,
    COUNT(DISTINCT p.id) as total_products,
    COUNT(DISTINCT o.id) as total_orders,
    COUNT(DISTINCT c.id) as total_customers,
    COALESCE(SUM(o.total_amount), 0) as total_revenue,
    s.view_count,
    s.created_at,
    s.last_activity
FROM stores s
LEFT JOIN products p ON p.store_id = s.id AND p.is_available = true
LEFT JOIN orders o ON o.store_id = s.id AND o.status NOT IN ('cancelled')
LEFT JOIN customers c ON c.store_id = s.id
WHERE s.is_active = true
GROUP BY s.id, s.name, s.user_id, s.view_count, s.created_at, s.last_activity;
