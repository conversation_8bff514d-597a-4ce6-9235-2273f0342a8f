// Constants for M-Duka app

export const APP_CONFIG = {
  name: '<PERSON>-<PERSON><PERSON>',
  version: '1.0.0',
  baseUrl: 'https://mduka.app',
  supportEmail: '<EMAIL>',
};

export const COLORS = {
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',
};

export const PRODUCT_CATEGORIES = [
  'Electronics',
  'Clothing & Fashion',
  'Food & Beverages',
  'Health & Beauty',
  'Home & Garden',
  'Sports & Outdoors',
  'Books & Media',
  'Toys & Games',
  'Automotive',
  'Services',
  'Other',
];

export const ORDER_STATUSES = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
} as const;

export const ORDER_STATUS_LABELS = {
  [ORDER_STATUSES.PENDING]: 'Pending',
  [ORDER_STATUSES.CONFIRMED]: 'Confirmed',
  [ORDER_STATUSES.DELIVERED]: 'Delivered',
  [ORDER_STATUSES.CANCELLED]: 'Cancelled',
};

export const ORDER_STATUS_COLORS = {
  [ORDER_STATUSES.PENDING]: 'bg-orange-100 text-orange-800',
  [ORDER_STATUSES.CONFIRMED]: 'bg-blue-100 text-blue-800',
  [ORDER_STATUSES.DELIVERED]: 'bg-green-100 text-green-800',
  [ORDER_STATUSES.CANCELLED]: 'bg-red-100 text-red-800',
};

export const CURRENCY = {
  symbol: 'KSh',
  code: 'KES',
  name: 'Kenyan Shilling',
};

export const VALIDATION_RULES = {
  storeName: {
    minLength: 2,
    maxLength: 100,
  },
  storeSlug: {
    minLength: 3,
    maxLength: 30,
    pattern: /^[a-z0-9-]+$/,
  },
  productName: {
    minLength: 2,
    maxLength: 200,
  },
  productPrice: {
    min: 0.01,
    max: 10000000,
  },
  description: {
    maxLength: 1000,
  },
  phoneNumber: {
    pattern: /^(\+254|0)[17]\d{8}$/,
  },
};

export const STORAGE_KEYS = {
  USER_TOKEN: 'userToken',
  ONBOARDING_COMPLETE: 'onboardingComplete',
  STORE_DATA: 'storeData',
  USER_PREFERENCES: 'userPreferences',
};

export const API_ENDPOINTS = {
  STORES: 'stores',
  PRODUCTS: 'products',
  ORDERS: 'orders',
};

export const WHATSAPP_TEMPLATES = {
  ORDER_CONFIRMATION: (storeName: string, orderItems: string, total: string) => 
    `Hello! I'd like to place an order from ${storeName}:\n\n${orderItems}\n\nTotal: ${total}\n\nPlease confirm my order. Thank you!`,
  
  ORDER_UPDATE: (orderNumber: string, status: string) =>
    `Order Update: Your order #${orderNumber} status has been updated to: ${status}`,
};

export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'sw', name: 'Kiswahili', flag: '🇰🇪' },
];

export const DEFAULT_BUSINESS_HOURS = 'Mon-Fri: 9AM-6PM, Sat: 9AM-4PM, Sun: Closed';

export const IMAGE_CONFIG = {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  quality: 0.8,
  maxWidth: 1024,
  maxHeight: 1024,
};
