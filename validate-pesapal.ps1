#!/usr/bin/env pwsh

# M-Duka Pesapal Integration Validation Script
# This script validates that all Pesapal integration components are properly configured

Write-Host "🔍 Validating M-Duka Pesapal Integration..." -ForegroundColor Green
Write-Host ""

$validationResults = @()

# Check required files exist
$requiredFiles = @(
    @{path="supabase-pesapal-schema.sql"; description="Database schema for Pesapal integration"},
    @{path="supabase\functions\pesapal-webhook\index.ts"; description="Pesapal webhook handler"},
    @{path="supabase\functions\pesapal-webhook\config.json"; description="Edge function configuration"},
    @{path="supabase\functions\pesapal-webhook\deno.json"; description="Deno configuration"},
    @{path="hooks\usePesapalSubscription.ts"; description="Pesapal subscription hook"},
    @{path="components\SubscriptionPlans.tsx"; description="Updated subscription plans component"},
    @{path="app\settings\upgrade-plan.tsx"; description="Updated upgrade plan page"}
)

Write-Host "📁 Checking required files..." -ForegroundColor Yellow
foreach ($file in $requiredFiles) {
    $fullPath = Join-Path $PWD $file.path
    if (Test-Path $fullPath) {
        Write-Host "✅ $($file.path) - $($file.description)" -ForegroundColor Green
        $validationResults += @{file=$file.path; status="Found"; description=$file.description}
    } else {
        Write-Host "❌ $($file.path) - $($file.description)" -ForegroundColor Red
        $validationResults += @{file=$file.path; status="Missing"; description=$file.description}
    }
}

Write-Host ""

# Check if old Polar files are removed
$oldFiles = @(
    "supabase\functions\polar-webhook\index.ts",
    "hooks\usePolarSubscription.ts"
)

Write-Host "🧹 Checking for old Polar files..." -ForegroundColor Yellow
$oldFilesExist = $false
foreach ($file in $oldFiles) {
    $fullPath = Join-Path $PWD $file
    if (Test-Path $fullPath) {
        Write-Host "⚠️  Old file still exists: $file" -ForegroundColor Yellow
        $oldFilesExist = $true
    }
}

if (-not $oldFilesExist) {
    Write-Host "✅ No old Polar files found" -ForegroundColor Green
}

Write-Host ""

# Check .env configuration
Write-Host "🔧 Checking environment configuration..." -ForegroundColor Yellow
$envPath = Join-Path $PWD ".env"
if (Test-Path $envPath) {
    $envContent = Get-Content $envPath -Raw
    if ($envContent -match "EXPO_PUBLIC_SUPABASE_URL") {
        Write-Host "✅ Supabase URL configured" -ForegroundColor Green
    } else {
        Write-Host "❌ Supabase URL not found in .env" -ForegroundColor Red
    }
    
    if ($envContent -match "EXPO_PUBLIC_SUPABASE_ANON_KEY") {
        Write-Host "✅ Supabase anon key configured" -ForegroundColor Green
    } else {
        Write-Host "❌ Supabase anon key not found in .env" -ForegroundColor Red
    }
} else {
    Write-Host "❌ .env file not found" -ForegroundColor Red
}

Write-Host ""

# Summary
Write-Host "📊 Validation Summary:" -ForegroundColor Cyan
Write-Host "═══════════════════════════════════════" -ForegroundColor Cyan

$foundFiles = ($validationResults | Where-Object {$_.status -eq "Found"}).Count
$missingFiles = ($validationResults | Where-Object {$_.status -eq "Missing"}).Count

Write-Host "✅ Files found: $foundFiles" -ForegroundColor Green
Write-Host "❌ Files missing: $missingFiles" -ForegroundColor Red

if ($missingFiles -eq 0) {
    Write-Host ""
    Write-Host "🎉 All required files are present!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Next steps:" -ForegroundColor Yellow
    Write-Host "1. Deploy database schema to Supabase SQL Editor"
    Write-Host "2. Deploy edge function via Supabase dashboard"  
    Write-Host "3. Set PESAPAL_WEBHOOK_SECRET environment variable"
    Write-Host "4. Configure webhook URL in Pesapal dashboard"
    Write-Host "5. Test the integration with a sample payment"
} else {
    Write-Host ""
    Write-Host "⚠️  Some files are missing. Please check the requirements." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔗 Important URLs:" -ForegroundColor Cyan
Write-Host "• Supabase SQL Editor: https://supabase.com/dashboard/project/vjwcnvdykoizguxoetgi/sql"
Write-Host "• Supabase Functions: https://supabase.com/dashboard/project/vjwcnvdykoizguxoetgi/functions"
Write-Host "• Your webhook URL: https://vjwcnvdykoizguxoetgi.supabase.co/functions/v1/pesapal-webhook"
