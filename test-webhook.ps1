# PowerShell test script for Polar webhook integration
# Usage: .\test-webhook.ps1 -Environment local -EventType created

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("local", "production")]
    [string]$Environment = "local",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("created", "updated", "cancelled")]
    [string]$EventType = "created"
)

# Configuration
$LocalUrl = "http://localhost:54321/functions/v1/polar-webhook"
$ProductionUrl = "https://your-project.functions.supabase.co/polar-webhook"
$WebhookSecret = "your-webhook-secret-here"

# Set URL based on environment
if ($Environment -eq "production") {
    $Url = $ProductionUrl
} else {
    $Url = $LocalUrl
}

# Set test data file
switch ($EventType) {
    "created" { $DataFile = "test-data/subscription-created.json" }
    "updated" { $DataFile = "test-data/subscription-updated.json" }
    "cancelled" { $DataFile = "test-data/subscription-cancelled.json" }
}

# Check if data file exists
if (-not (Test-Path $DataFile)) {
    Write-Error "Test data file $DataFile not found"
    exit 1
}

# Read the JSON data
$JsonData = Get-Content $DataFile -Raw

# Generate HMAC signature
$hmacsha = New-Object System.Security.Cryptography.HMACSHA256
$hmacsha.Key = [Text.Encoding]::UTF8.GetBytes($WebhookSecret)
$hash = $hmacsha.ComputeHash([Text.Encoding]::UTF8.GetBytes($JsonData))
$signature = "sha256=" + [BitConverter]::ToString($hash).Replace("-", "").ToLower()

Write-Host "Testing webhook with:"
Write-Host "URL: $Url"
Write-Host "Event: $EventType"
Write-Host "Data file: $DataFile"
Write-Host "Signature: $signature"
Write-Host ""

# Send the webhook request
try {
    $headers = @{
        "Content-Type" = "application/json"
        "polar-webhook-signature" = $signature
    }
    
    $response = Invoke-RestMethod -Uri $Url -Method Post -Body $JsonData -Headers $headers
    Write-Host "Success! Response:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 10)
}
catch {
    Write-Host "Error occurred:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}

Write-Host ""
Write-Host "Test completed!"
