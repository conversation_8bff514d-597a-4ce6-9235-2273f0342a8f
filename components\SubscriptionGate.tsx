import React from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useSubscription } from '../hooks/useSubscription';

interface SubscriptionGateProps {
  children: React.ReactNode;
  feature?: string;
  planRequired?: 'Basic' | 'Standard' | 'Premium';
  checkLimit?: 'products' | 'orders' | 'storage';
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
}

export function SubscriptionGate({
  children,
  feature,
  planRequired,
  checkLimit,
  fallback,
  showUpgradePrompt = true,
}: SubscriptionGateProps) {
  const {
    subscription,
    limits,
    usage,
    loading,
    checkProductLimit,
    checkOrderLimit,
    checkStorageLimit,
    isFeatureAvailable,
    isSubscriptionActive,
  } = useSubscription();

  if (loading) {
    return null;
  }

  // Check if subscription is active
  if (!isSubscriptionActive()) {
    return showUpgradePrompt ? (
      <UpgradePrompt
        title="Subscription Required"
        message="You need an active subscription to access this feature."
      />
    ) : (
      fallback || null
    );
  }

  // Check specific feature availability
  if (feature && !isFeatureAvailable(feature)) {
    return showUpgradePrompt ? (
      <UpgradePrompt
        title="Upgrade Required"
        message={`The "${feature}" feature is not available in your current plan.`}
      />
    ) : (
      fallback || null
    );
  }

  // Check plan level requirement
  if (planRequired && subscription?.plan) {
    const planHierarchy = { Basic: 1, Standard: 2, Premium: 3 };
    const currentPlanLevel = planHierarchy[subscription.plan.name as keyof typeof planHierarchy] || 0;
    const requiredLevel = planHierarchy[planRequired];

    if (currentPlanLevel < requiredLevel) {
      return showUpgradePrompt ? (
        <UpgradePrompt
          title="Upgrade Required"
          message={`This feature requires the ${planRequired} plan or higher.`}
        />
      ) : (
        fallback || null
      );
    }
  }

  // Check usage limits
  if (checkLimit) {
    let limitExceeded = false;
    let limitMessage = '';

    switch (checkLimit) {
      case 'products':
        limitExceeded = !checkProductLimit();
        limitMessage = `You've reached your product limit (${limits?.maxProducts}). Upgrade to add more products.`;
        break;
      case 'orders':
        limitExceeded = !checkOrderLimit();
        limitMessage = `You've reached your order limit (${limits?.maxOrders}). Upgrade to process more orders.`;
        break;
      case 'storage':
        limitExceeded = !checkStorageLimit();
        limitMessage = `You've reached your storage limit (${limits?.maxStorageGb}GB). Upgrade for more storage.`;
        break;
    }

    if (limitExceeded) {
      return showUpgradePrompt ? (
        <UpgradePrompt title="Limit Reached" message={limitMessage} />
      ) : (
        fallback || null
      );
    }
  }

  // All checks passed, render children
  return <>{children}</>;
}

interface UpgradePromptProps {
  title: string;
  message: string;
}

function UpgradePrompt({ title, message }: UpgradePromptProps) {
  const handleUpgrade = () => {
    Alert.alert(
      'Upgrade Subscription',
      'Would you like to upgrade your subscription plan?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Upgrade',
          onPress: () => router.push('/settings/upgrade-plan'),
        },
      ]
    );
  };

  return (
    <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 m-4">
      <View className="flex-row items-center mb-2">
        <Ionicons name="lock-closed" size={20} color="#D97706" />
        <Text className="text-yellow-800 font-semibold ml-2">{title}</Text>
      </View>
      <Text className="text-yellow-700 mb-3">{message}</Text>
      <TouchableOpacity
        onPress={handleUpgrade}
        className="bg-yellow-600 py-2 px-4 rounded-md"
      >
        <Text className="text-white font-medium text-center">Upgrade Now</Text>
      </TouchableOpacity>
    </View>
  );
}

// HOC version for wrapping entire screens
export function withSubscriptionGate<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  gateProps: Omit<SubscriptionGateProps, 'children'>
) {
  return function SubscriptionGatedComponent(props: P) {
    return (
      <SubscriptionGate {...gateProps}>
        <WrappedComponent {...props} />
      </SubscriptionGate>
    );
  };
}
