-- Additional schema updates for Polar integration
-- Add this to your existing schema

-- Update user_subscriptions table to support Polar
ALTER TABLE user_subscriptions 
ADD COLUMN IF NOT EXISTS polar_subscription_id VARCHAR(255) UNIQUE,
ADD COLUMN IF NOT EXISTS polar_customer_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS billing_cycle VARCHAR(20) DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly'));

-- <PERSON>reate webhook events log table for debugging and monitoring
CREATE TABLE IF NOT EXISTS webhook_events (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    polar_event_id VARCHAR(255) UNIQUE,
    payload JSONB NOT NULL,
    processed BOOLEAN DEFAULT false,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Update subscription plans to match your Polar plans
INSERT INTO subscription_plans (name, price_monthly, price_yearly, max_products, max_orders, max_storage_gb, features) VALUES
('basic', 0.00, 0.00, 10, 50, 1, '["Basic Store", "WhatsApp Integration", "Basic Analytics"]'),
('starter', 19.99, 199.99, 100, 500, 10, '["Everything in Basic", "Advanced Analytics", "Custom Domain", "Priority Support"]'),
('classhero', 39.99, 399.99, -1, -1, 100, '["Everything in Starter", "White-label Solution", "API Access", "24/7 Support", "Advanced Features"]')
ON CONFLICT (name) DO UPDATE SET
    price_monthly = EXCLUDED.price_monthly,
    price_yearly = EXCLUDED.price_yearly,
    max_products = EXCLUDED.max_products,
    max_orders = EXCLUDED.max_orders,
    max_storage_gb = EXCLUDED.max_storage_gb,
    features = EXCLUDED.features;

-- Create indexes for Polar fields
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_polar_subscription_id ON user_subscriptions(polar_subscription_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_polar_customer_id ON user_subscriptions(polar_customer_id);
CREATE INDEX IF NOT EXISTS idx_webhook_events_polar_event_id ON webhook_events(polar_event_id);
CREATE INDEX IF NOT EXISTS idx_webhook_events_processed ON webhook_events(processed);

-- Enable RLS for webhook_events
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;

-- Admin-only access to webhook events (for debugging)
CREATE POLICY "Service role can manage webhook events" ON webhook_events
    USING (auth.role() = 'service_role')
    WITH CHECK (auth.role() = 'service_role');
