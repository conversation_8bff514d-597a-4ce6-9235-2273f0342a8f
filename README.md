# M-Duka - Mobile-First eCommerce App

M-Duka is a lightweight, single-user online store SaaS that allows small business owners to set up and manage their own mobile-friendly online shop with a unique public URL.

## 🎯 Features

- **User Authentication**: Secure signup/login with Supabase
- **Store Management**: Create and customize your store
- **Product Management**: Add, edit, and manage products
- **Order Management**: Receive and track customer orders
- **WhatsApp Integration**: Direct customer communication
- **Public Store Pages**: Unique URLs for each store
- **Real-time Updates**: Live order notifications
- **Mobile-First Design**: Optimized for mobile devices

## 🛠️ Tech Stack

- **Frontend**: React Native + Expo
- **Backend**: Supabase (Auth + Database)
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **Navigation**: Expo Router
- **UI Components**: React Native Paper + Custom Components
- **State Management**: React Hooks + Supabase Real-time

## 📱 App Structure

```
M-Duka/
├── app/                    # Expo Router pages
│   ├── (tabs)/            # Main app tabs
│   │   ├── index.tsx      # Dashboard
│   │   ├── products.tsx   # Products management
│   │   ├── orders.tsx     # Orders management
│   │   ├── store.tsx      # Store overview
│   │   └── settings.tsx   # App settings
│   ├── auth/              # Authentication screens
│   │   ├── login.tsx      # Login screen
│   │   └── signup.tsx     # Signup screen
│   ├── onboarding.tsx     # Store setup
│   ├── _layout.tsx        # Root layout
│   └── index.tsx          # Entry point
├── lib/                   # Utilities and configurations
│   └── supabase.ts        # Supabase client and types
├── components/            # Reusable components (to be added)
├── assets/                # Images and static files
└── global.css             # Global styles
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Expo CLI
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd M-Duka
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**
   - Create a new project at [supabase.com](https://supabase.com)
   - Copy the project URL and anon key
   - Run the SQL schema from `supabase-schema.sql` in your Supabase SQL editor

4. **Configure environment variables**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` and add your Supabase credentials:
   ```
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

5. **Start the development server**
   ```bash
   npm start
   ```

6. **Run on device/simulator**
   - Install Expo Go app on your phone
   - Scan the QR code from the terminal
   - Or press `a` for Android emulator, `i` for iOS simulator

## 📊 Database Schema

The app uses the following main tables:

- **stores**: Store information and settings
- **products**: Product catalog for each store
- **orders**: Customer orders and status tracking

See `supabase-schema.sql` for the complete database schema.

## 🔧 Configuration

### Supabase Setup

1. Create tables using the provided SQL schema
2. Enable Row Level Security (RLS) policies
3. Configure authentication providers if needed
4. Set up real-time subscriptions for orders

### Environment Variables

- `EXPO_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `EXPO_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `EXPO_PUBLIC_APP_NAME`: App name (default: M-Duka)
- `EXPO_PUBLIC_BASE_URL`: Base URL for store links

## 📱 Usage

### For Store Owners

1. **Sign Up**: Create an account
2. **Onboarding**: Set up your store details
3. **Add Products**: Upload product images and details
4. **Manage Orders**: Track and update order status
5. **Share Store**: Share your unique store URL

### For Customers

1. Visit store URL (e.g., `mduka.app/storename`)
2. Browse products
3. Place orders via WhatsApp
4. Receive order confirmations

## 🚀 Deployment

### Using EAS Build

1. **Install EAS CLI**
   ```bash
   npm install -g @expo/eas-cli
   ```

2. **Configure EAS**
   ```bash
   eas build:configure
   ```

3. **Build for production**
   ```bash
   eas build --platform all
   ```

4. **Submit to app stores**
   ```bash
   eas submit
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Contact: [<EMAIL>]

## 🔮 Roadmap

- [ ] Multi-language support (Swahili/English)
- [ ] Advanced analytics dashboard
- [ ] Inventory management
- [ ] Multiple payment gateways
- [ ] Custom domain support
- [ ] Push notifications
- [ ] Bulk product import
- [ ] Store themes and customization
