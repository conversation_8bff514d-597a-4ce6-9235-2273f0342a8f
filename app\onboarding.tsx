import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { supabase } from '../lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function Onboarding() {
  const [storeName, setStoreName] = useState('');
  const [storeSlug, setStoreSlug] = useState('');
  const [description, setDescription] = useState('');
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [loading, setLoading] = useState(false);

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 30);
  };

  const handleStoreNameChange = (name: string) => {
    setStoreName(name);
    if (name) {
      setStoreSlug(generateSlug(name));
    } else {
      setStoreSlug('');
    }
  };

  const checkSlugAvailability = async (slug: string) => {
    const { data, error } = await supabase
      .from('stores')
      .select('slug')
      .eq('slug', slug)
      .single();

    return !data; // Returns true if slug is available
  };

  const createStore = async () => {
    if (!storeName.trim()) {
      Alert.alert('Error', 'Please enter a store name');
      return;
    }

    if (!storeSlug.trim()) {
      Alert.alert('Error', 'Please enter a store URL');
      return;
    }

    setLoading(true);

    try {
      // Check if slug is available
      const isSlugAvailable = await checkSlugAvailability(storeSlug);
      if (!isSlugAvailable) {
        Alert.alert('Error', 'This store URL is already taken. Please choose another one.');
        setLoading(false);
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        Alert.alert('Error', 'User not authenticated');
        setLoading(false);
        return;
      }

      // Create store
      const { data: store, error } = await supabase
        .from('stores')
        .insert({
          user_id: user.id,
          name: storeName.trim(),
          slug: storeSlug.trim(),
          description: description.trim() || null,
          whatsapp_number: whatsappNumber.trim() || null,
          is_active: true,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating store:', error);
        Alert.alert('Error', 'Failed to create store. Please try again.');
        return;
      }

      // Store creation successful - onboarding is complete
      
      Alert.alert(
        'Success!',
        `Your store "${storeName}" has been created successfully!`,
        [
          {
            text: 'Continue',
            onPress: () => router.replace('/(tabs)'),
          },
        ]
      );
    } catch (error) {
      console.error('Error in onboarding:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <ScrollView className="flex-1 px-6 py-4">
        <View className="mb-8">
          <Text className="text-3xl font-bold text-gray-900 mb-2">Set Up Your Store</Text>
          <Text className="text-gray-600">
            Let's create your M-Duka store. You can always change these details later.
          </Text>
        </View>

        <View className="space-y-6">
          {/* Store Name */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Store Name *</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="e.g., John's Electronics"
              value={storeName}
              onChangeText={handleStoreNameChange}
            />
          </View>

          {/* Store URL */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Store URL *</Text>
            <View className="flex-row items-center border border-gray-300 rounded-lg">
              <Text className="px-4 py-3 text-gray-500 bg-gray-50 rounded-l-lg">
                mduka.app/
              </Text>
              <TextInput
                className="flex-1 px-4 py-3 text-gray-900"
                placeholder="your-store-name"
                value={storeSlug}
                onChangeText={setStoreSlug}
                autoCapitalize="none"
              />
            </View>
            <Text className="text-gray-500 text-sm mt-1">
              This will be your store's public URL
            </Text>
          </View>

          {/* Description */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Store Description</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="Tell customers about your store..."
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          {/* WhatsApp Number */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">WhatsApp Number</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="e.g., +254712345678"
              value={whatsappNumber}
              onChangeText={setWhatsappNumber}
              keyboardType="phone-pad"
            />
            <Text className="text-gray-500 text-sm mt-1">
              Customers will use this to contact you for orders
            </Text>
          </View>

          {/* Create Store Button */}
          <TouchableOpacity
            className={`bg-blue-600 rounded-lg py-4 mt-8 ${loading ? 'opacity-50' : ''}`}
            onPress={createStore}
            disabled={loading}
          >
            <Text className="text-white text-center font-semibold text-lg">
              {loading ? 'Creating Store...' : 'Create My Store'}
            </Text>
          </TouchableOpacity>

          {/* Info Box */}
          <View className="bg-blue-50 rounded-lg p-4 mt-6">
            <Text className="text-blue-800 font-medium mb-2">What happens next?</Text>
            <Text className="text-blue-700 text-sm">
              • Your store will be created with a unique URL{'\n'}
              • You can start adding products immediately{'\n'}
              • Customers can visit your store and place orders{'\n'}
              • You'll receive orders via WhatsApp
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
