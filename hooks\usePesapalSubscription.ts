import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';

export interface PesapalSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: 'active' | 'cancelled' | 'expired' | 'trial' | 'pending';
  pesapal_tracking_id?: string;
  pesapal_merchant_reference?: string;
  pesapal_product_code?: string;
  current_period_start: string;
  current_period_end: string;
  trial_end?: string;
  billing_cycle: 'monthly' | 'yearly';
  plan?: {
    id: string;
    name: 'basic' | 'premium' | 'business';
    price_monthly: number;
    price_yearly: number;
    max_products: number;
    max_orders: number;
    max_storage_gb: number;
    features: string[];
    pesapal_product_code: string;
    pesapal_product_url: string;
  };
}

export interface SubscriptionLimits {
  maxProducts: number;
  maxOrders: number;
  maxStorageGb: number;
  features: string[];
  canAddProducts: boolean;
  canProcessOrders: boolean;
  hasAdvancedAnalytics: boolean;
  hasCustomDomain: boolean;
  hasPrioritySupport: boolean;
  hasWhiteLabel: boolean;
}

export function usePesapalSubscription() {
  const [subscription, setSubscription] = useState<PesapalSubscription | null>(null);  const [limits, setLimits] = useState<SubscriptionLimits | null>(null);
  const [usage, setUsage] = useState({ products: 0, orders: 0, storageUsed: 0 });
  const [loading, setLoading] = useState(true);

  // Get Pesapal payment URL for upgrading
  const getPesapalPaymentUrl = (planName: 'basic' | 'premium' | 'business'): string => {
    const productCodes = {
      basic: 'ff4ab78a-b437-4901-baa0-736b41efcf32',
      premium: '2186b965-2fb5-44da-952c-3dd6704270cc',
      business: '77d32354-4ba7-4a21-9f62-04272852aca8'
    };

    return `https://store.pesapal.com/shop/svdp2z-omitechgroupcompanylimited?productCode=${productCodes[planName]}`;
  };

  useEffect(() => {
    fetchSubscriptionData();
    
    // Set up real-time subscription for subscription changes
    const subscription = supabase
      .channel('subscription-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_subscriptions',
        },
        () => {
          fetchSubscriptionData();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setLoading(false);
        return;
      }

      // Get subscription with plan details
      const { data: subscriptionData, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          plan:subscription_plans(*)
        `)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching subscription:', error);
        setLoading(false);
        return;
      }

      setSubscription(subscriptionData);

      if (subscriptionData?.plan) {
        const plan = subscriptionData.plan;
        setLimits({
          maxProducts: plan.max_products,
          maxOrders: plan.max_orders,
          maxStorageGb: plan.max_storage_gb,
          features: plan.features,
          canAddProducts: plan.max_products === -1 || usage.products < plan.max_products,
          canProcessOrders: plan.max_orders === -1 || usage.orders < plan.max_orders,
          hasAdvancedAnalytics: plan.features.includes('Advanced Analytics'),
          hasCustomDomain: plan.features.includes('Custom Domain'),
          hasPrioritySupport: plan.features.includes('Priority Support'),
          hasWhiteLabel: plan.features.includes('White-label Solution'),
        });
      }

      // Fetch usage stats
      await fetchUsageStats(user.id);
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUsageStats = async (userId: string) => {
    try {
      // Get user's store
      const { data: store } = await supabase
        .from('stores')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (!store) return;

      // Get product count
      const { count: productCount } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('store_id', store.id);

      // Get order count
      const { count: orderCount } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('store_id', store.id);

      // TODO: Calculate storage usage from uploaded images
      const storageUsed = 0;

      setUsage({
        products: productCount || 0,
        orders: orderCount || 0,
        storageUsed,
      });

      // Update limits with current usage
      if (limits) {
        setLimits(prev => prev ? {
          ...prev,
          canAddProducts: prev.maxProducts === -1 || (productCount || 0) < prev.maxProducts,
          canProcessOrders: prev.maxOrders === -1 || (orderCount || 0) < prev.maxOrders,
        } : null);
      }
    } catch (error) {
      console.error('Error fetching usage stats:', error);
    }
  };

  const getPlanName = (): string => {
    return subscription?.plan?.name || 'basic';
  };

  const isSubscriptionActive = (): boolean => {
    return subscription?.status === 'active' || subscription?.status === 'trial';
  };

  const checkFeatureAccess = (feature: string): boolean => {
    if (!limits) return false;
    return limits.features.includes(feature);
  };

  const checkProductLimit = (): boolean => {
    if (!limits) return true;
    return limits.maxProducts === -1 || usage.products < limits.maxProducts;
  };

  const checkOrderLimit = (): boolean => {
    if (!limits) return true;
    return limits.maxOrders === -1 || usage.orders < limits.maxOrders;
  };

  const checkStorageLimit = (additionalGb: number = 0): boolean => {
    if (!limits) return true;
    return (usage.storageUsed + additionalGb) <= limits.maxStorageGb;
  };

  const refresh = () => {
    fetchSubscriptionData();
  };
  return {
    subscription,
    limits,
    usage,
    loading,
    getPlanName,
    isSubscriptionActive,
    checkFeatureAccess,
    checkProductLimit,
    checkOrderLimit,
    checkStorageLimit,
    getPesapalPaymentUrl,
    refresh,
  };
}
