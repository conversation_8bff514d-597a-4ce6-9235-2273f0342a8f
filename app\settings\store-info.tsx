import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { supabase, Store } from '../../lib/supabase';

export default function StoreInfo() {
  const [store, setStore] = useState<Store | null>(null);
  const [storeName, setStoreName] = useState('');
  const [description, setDescription] = useState('');
  const [businessHours, setBusinessHours] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    loadStoreInfo();
  }, []);

  const loadStoreInfo = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: storeData, error } = await supabase
        .from('stores')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error loading store:', error);
        return;
      }

      setStore(storeData);
      setStoreName(storeData.name);
      setDescription(storeData.description || '');
      setBusinessHours(storeData.business_hours || '');
    } catch (error) {
      console.error('Error loading store info:', error);
    } finally {
      setInitialLoading(false);
    }
  };

  const updateStoreInfo = async () => {
    if (!storeName.trim()) {
      Alert.alert('Error', 'Please enter a store name');
      return;
    }

    if (!store) return;

    setLoading(true);

    try {
      const { error } = await supabase
        .from('stores')
        .update({
          name: storeName.trim(),
          description: description.trim() || null,
          business_hours: businessHours.trim() || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', store.id);

      if (error) {
        console.error('Error updating store:', error);
        Alert.alert('Error', 'Failed to update store information');
        return;
      }

      Alert.alert('Success', 'Store information updated successfully!');
    } catch (error) {
      console.error('Error updating store:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 justify-center items-center">
          <Text className="text-gray-600">Loading store information...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-row items-center px-6 py-4 border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900 ml-4">Store Information</Text>
      </View>

      <ScrollView className="flex-1 px-6 py-4">
        <View className="space-y-6">
          {/* Store Name */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Store Name *</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="Enter your store name"
              value={storeName}
              onChangeText={setStoreName}
            />
          </View>

          {/* Store URL (Read-only) */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Store URL</Text>
            <View className="border border-gray-200 rounded-lg px-4 py-3 bg-gray-50">
              <Text className="text-gray-600">
                mduka.app/{store?.slug}
              </Text>
            </View>
            <Text className="text-gray-500 text-sm mt-1">
              Your store URL cannot be changed after creation
            </Text>
          </View>

          {/* Description */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Store Description</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="Tell customers about your store..."
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
            <Text className="text-gray-500 text-sm mt-1">
              This will be displayed on your public store page
            </Text>
          </View>

          {/* Business Hours */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Business Hours</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="e.g., Mon-Fri: 9AM-6PM, Sat: 9AM-4PM"
              value={businessHours}
              onChangeText={setBusinessHours}
              multiline
              numberOfLines={2}
              textAlignVertical="top"
            />
            <Text className="text-gray-500 text-sm mt-1">
              Let customers know when your store is open
            </Text>
          </View>

          {/* Store Status */}
          <View>
            <Text className="text-gray-700 font-medium mb-3">Store Status</Text>
            <View className={`p-4 rounded-lg border ${
              store?.is_active 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <View className="flex-row items-center">
                <View className={`w-3 h-3 rounded-full mr-3 ${
                  store?.is_active ? 'bg-green-500' : 'bg-red-500'
                }`} />
                <Text className={`font-medium ${
                  store?.is_active ? 'text-green-700' : 'text-red-700'
                }`}>
                  {store?.is_active ? 'Store is Open' : 'Store is Closed'}
                </Text>
              </View>
              <Text className={`text-sm mt-1 ${
                store?.is_active ? 'text-green-600' : 'text-red-600'
              }`}>
                {store?.is_active 
                  ? 'Customers can view and order from your store'
                  : 'Your store is not visible to customers'
                }
              </Text>
            </View>
          </View>

          {/* Created Date */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Store Created</Text>
            <View className="border border-gray-200 rounded-lg px-4 py-3 bg-gray-50">
              <Text className="text-gray-600">
                {store ? new Date(store.created_at).toLocaleDateString() : 'N/A'}
              </Text>
            </View>
          </View>

          {/* Update Button */}
          <TouchableOpacity
            className={`bg-blue-600 rounded-lg py-4 mt-8 ${loading ? 'opacity-50' : ''}`}
            onPress={updateStoreInfo}
            disabled={loading}
          >
            <Text className="text-white text-center font-semibold text-lg">
              {loading ? 'Updating...' : 'Update Store Information'}
            </Text>
          </TouchableOpacity>

          {/* Info Box */}
          <View className="bg-blue-50 rounded-lg p-4 mt-6">
            <View className="flex-row items-start">
              <Ionicons name="information-circle" size={20} color="#3b82f6" />
              <View className="ml-3 flex-1">
                <Text className="text-blue-800 font-medium mb-1">Tips for a great store</Text>
                <Text className="text-blue-700 text-sm">
                  • Use a clear, memorable store name{'\n'}
                  • Write a compelling description that explains what you sell{'\n'}
                  • Keep your business hours up to date{'\n'}
                  • Make sure your store status reflects your availability
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
