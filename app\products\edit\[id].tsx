import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, ScrollView, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { supabase, Product } from '../../../lib/supabase';

export default function EditProduct() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [price, setPrice] = useState('');
  const [category, setCategory] = useState('');
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [isAvailable, setIsAvailable] = useState(true);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadProduct();
    }
  }, [id]);

  const loadProduct = async () => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        Alert.alert('Error', 'Failed to load product');
        router.back();
        return;
      }

      setProduct(data);
      setName(data.name);
      setDescription(data.description || '');
      setPrice(data.price.toString());
      setCategory(data.category || '');
      setImageUri(data.image_url);
      setIsAvailable(data.is_available);
    } catch (error) {
      console.error('Error loading product:', error);
      Alert.alert('Error', 'Failed to load product');
      router.back();
    } finally {
      setInitialLoading(false);
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImageUri(result.assets[0].uri);
    }
  };

  const updateProduct = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a product name');
      return;
    }

    if (!price.trim() || isNaN(Number(price))) {
      Alert.alert('Error', 'Please enter a valid price');
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase
        .from('products')
        .update({
          name: name.trim(),
          description: description.trim() || null,
          price: parseFloat(price),
          category: category.trim() || null,
          image_url: imageUri,
          is_available: isAvailable,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);

      if (error) {
        console.error('Error updating product:', error);
        Alert.alert('Error', 'Failed to update product');
        return;
      }

      Alert.alert('Success', 'Product updated successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Error updating product:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 justify-center items-center">
          <Text className="text-gray-600">Loading product...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-row items-center px-6 py-4 border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900 ml-4">Edit Product</Text>
      </View>

      <ScrollView className="flex-1 px-6 py-4">
        <View className="space-y-6">
          {/* Product Image */}
          <View>
            <Text className="text-gray-700 font-medium mb-3">Product Image</Text>
            <TouchableOpacity
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 items-center"
              onPress={pickImage}
            >
              {imageUri ? (
                <Image source={{ uri: imageUri }} className="w-32 h-32 rounded-lg" />
              ) : (
                <>
                  <Ionicons name="camera" size={48} color="#9ca3af" />
                  <Text className="text-gray-500 mt-2">Tap to add image</Text>
                </>
              )}
            </TouchableOpacity>
          </View>

          {/* Product Name */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Product Name *</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="Enter product name"
              value={name}
              onChangeText={setName}
            />
          </View>

          {/* Description */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Description</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="Describe your product..."
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          {/* Price */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Price (KSh) *</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="0.00"
              value={price}
              onChangeText={setPrice}
              keyboardType="numeric"
            />
          </View>

          {/* Category */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">Category</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="e.g., Electronics, Clothing, Food"
              value={category}
              onChangeText={setCategory}
            />
          </View>

          {/* Availability Toggle */}
          <View>
            <Text className="text-gray-700 font-medium mb-3">Availability</Text>
            <TouchableOpacity
              className={`flex-row items-center p-4 rounded-lg border ${
                isAvailable ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
              }`}
              onPress={() => setIsAvailable(!isAvailable)}
            >
              <View className={`w-6 h-6 rounded-full mr-3 ${
                isAvailable ? 'bg-green-500' : 'bg-red-500'
              } items-center justify-center`}>
                <Ionicons 
                  name={isAvailable ? "checkmark" : "close"} 
                  size={16} 
                  color="white" 
                />
              </View>
              <Text className={`font-medium ${
                isAvailable ? 'text-green-700' : 'text-red-700'
              }`}>
                {isAvailable ? 'Available for sale' : 'Out of stock'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Update Product Button */}
          <TouchableOpacity
            className={`bg-blue-600 rounded-lg py-4 mt-8 ${loading ? 'opacity-50' : ''}`}
            onPress={updateProduct}
            disabled={loading}
          >
            <Text className="text-white text-center font-semibold text-lg">
              {loading ? 'Updating Product...' : 'Update Product'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
