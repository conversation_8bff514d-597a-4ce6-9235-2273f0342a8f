-- Row Level Security (RLS) Policies for M-Duka
-- This file sets up security policies for all tables

-- Enable RLS on all tables
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;

-- =============================================
-- SUBSCRIPTION PLANS POLICIES
-- =============================================

-- Anyone can read active subscription plans
CREATE POLICY "subscription_plans_select_public" ON subscription_plans
    FOR SELECT USING (is_active = true);

-- Only service role can modify subscription plans
CREATE POLICY "subscription_plans_admin_all" ON subscription_plans
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =============================================
-- USER SUBSCRIPTIONS POLICIES
-- =============================================

-- Users can read their own subscriptions
CREATE POLICY "user_subscriptions_select_own" ON user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own subscriptions
CREATE POLICY "user_subscriptions_insert_own" ON user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own subscriptions
CREATE POLICY "user_subscriptions_update_own" ON user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- Service role can do everything with subscriptions
CREATE POLICY "user_subscriptions_service_all" ON user_subscriptions
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =============================================
-- STORES POLICIES
-- =============================================

-- Users can read their own stores
CREATE POLICY "stores_select_own" ON stores
    FOR SELECT USING (auth.uid() = user_id);

-- Anyone can read public stores
CREATE POLICY "stores_select_public" ON stores
    FOR SELECT USING (is_public = true AND is_active = true);

-- Users can insert their own stores
CREATE POLICY "stores_insert_own" ON stores
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own stores
CREATE POLICY "stores_update_own" ON stores
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own stores
CREATE POLICY "stores_delete_own" ON stores
    FOR DELETE USING (auth.uid() = user_id);

-- =============================================
-- PRODUCT CATEGORIES POLICIES
-- =============================================

-- Users can read categories from their own stores
CREATE POLICY "product_categories_select_own" ON product_categories
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = product_categories.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- Anyone can read categories from public stores
CREATE POLICY "product_categories_select_public" ON product_categories
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = product_categories.store_id 
            AND stores.is_public = true 
            AND stores.is_active = true
        )
    );

-- Users can manage categories in their own stores
CREATE POLICY "product_categories_manage_own" ON product_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = product_categories.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- =============================================
-- PRODUCTS POLICIES
-- =============================================

-- Users can read products from their own stores
CREATE POLICY "products_select_own" ON products
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = products.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- Anyone can read available products from public stores
CREATE POLICY "products_select_public" ON products
    FOR SELECT USING (
        is_available = true AND
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = products.store_id 
            AND stores.is_public = true 
            AND stores.is_active = true
        )
    );

-- Users can manage products in their own stores
CREATE POLICY "products_manage_own" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = products.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- =============================================
-- CUSTOMERS POLICIES
-- =============================================

-- Users can read customers from their own stores
CREATE POLICY "customers_select_own" ON customers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = customers.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- Users can manage customers in their own stores
CREATE POLICY "customers_manage_own" ON customers
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = customers.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- =============================================
-- ORDERS POLICIES
-- =============================================

-- Users can read orders from their own stores
CREATE POLICY "orders_select_own" ON orders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = orders.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- Anyone can create orders for public stores
CREATE POLICY "orders_insert_public" ON orders
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = orders.store_id 
            AND stores.is_public = true 
            AND stores.is_active = true
        )
    );

-- Users can update orders in their own stores
CREATE POLICY "orders_update_own" ON orders
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = orders.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- Users can delete orders from their own stores
CREATE POLICY "orders_delete_own" ON orders
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = orders.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- =============================================
-- ORDER ITEMS POLICIES
-- =============================================

-- Users can read order items from their own stores
CREATE POLICY "order_items_select_own" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders 
            JOIN stores ON stores.id = orders.store_id
            WHERE orders.id = order_items.order_id 
            AND stores.user_id = auth.uid()
        )
    );

-- Anyone can create order items for public stores
CREATE POLICY "order_items_insert_public" ON order_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM orders 
            JOIN stores ON stores.id = orders.store_id
            WHERE orders.id = order_items.order_id 
            AND stores.is_public = true 
            AND stores.is_active = true
        )
    );

-- Users can update order items in their own stores
CREATE POLICY "order_items_update_own" ON order_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM orders 
            JOIN stores ON stores.id = orders.store_id
            WHERE orders.id = order_items.order_id 
            AND stores.user_id = auth.uid()
        )
    );

-- Users can delete order items from their own stores
CREATE POLICY "order_items_delete_own" ON order_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM orders 
            JOIN stores ON stores.id = orders.store_id
            WHERE orders.id = order_items.order_id 
            AND stores.user_id = auth.uid()
        )
    );

-- =============================================
-- WEBHOOK EVENTS POLICIES
-- =============================================

-- Users can read their own webhook events
CREATE POLICY "webhook_events_select_own" ON webhook_events
    FOR SELECT USING (auth.uid() = user_id);

-- Service role can manage all webhook events
CREATE POLICY "webhook_events_service_all" ON webhook_events
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =============================================
-- ANALYTICS EVENTS POLICIES
-- =============================================

-- Users can read analytics from their own stores
CREATE POLICY "analytics_events_select_own" ON analytics_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = analytics_events.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- Anyone can insert analytics events for public stores
CREATE POLICY "analytics_events_insert_public" ON analytics_events
    FOR INSERT WITH CHECK (
        store_id IS NULL OR
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = analytics_events.store_id 
            AND stores.is_public = true 
            AND stores.is_active = true
        )
    );

-- Users can manage analytics in their own stores
CREATE POLICY "analytics_events_manage_own" ON analytics_events
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = analytics_events.store_id 
            AND stores.user_id = auth.uid()
        )
    );
