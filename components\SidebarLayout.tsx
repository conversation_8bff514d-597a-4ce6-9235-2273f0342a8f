import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { router, usePathname } from 'expo-router';
import { supabase } from '../lib/supabase';

const { width } = Dimensions.get('window');
const SIDEBAR_WIDTH = 280;

interface MenuItem {
  id: string;
  title: string;
  icon: string;
  route: string;
  badge?: string;
  badgeColor?: string;
  hasSubmenu?: boolean;
}

interface SidebarLayoutProps {
  children: React.ReactNode;
}

export default function SidebarLayout({ children }: SidebarLayoutProps) {
  const [userInfo, setUserInfo] = useState<{ name: string; email: string } | null>(null);
  const [sidebarVisible, setSidebarVisible] = useState(width > 768);
  const pathname = usePathname();

  useEffect(() => {
    loadUserInfo();
  }, []);

  const loadUserInfo = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        // Get store info for display name
        const { data: store } = await supabase
          .from('stores')
          .select('name')
          .eq('user_id', user.id)
          .single();

        setUserInfo({
          name: store?.name || 'Store Owner',
          email: user.email || '<EMAIL>'
        });
      }
    } catch (error) {
      console.error('Error loading user info:', error);
    }
  };

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: 'home-outline',
      route: '/(tabs)',
    },
    {
      id: 'orders',
      title: 'Orders',
      icon: 'receipt-outline',
      route: '/(tabs)/orders',
      hasSubmenu: true,
    },
    {
      id: 'products',
      title: 'Products',
      icon: 'cube-outline',
      route: '/(tabs)/products',
      hasSubmenu: true,
    },
    {
      id: 'customers',
      title: 'Customers',
      icon: 'people-outline',
      route: '/(tabs)/customers',
      hasSubmenu: true,
    },
    {
      id: 'inbox',
      title: 'Inbox',
      icon: 'chatbubble-outline',
      route: '/(tabs)/inbox',
      hasSubmenu: true,
    },
    {
      id: 'design',
      title: 'Design',
      icon: 'brush-outline',
      route: '/(tabs)/design',
      hasSubmenu: true,
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: 'settings-outline',
      route: '/(tabs)/settings',
    },
  ];

  const appsMenuItems: MenuItem[] = [
    {
      id: 'discounts',
      title: 'Discounts',
      icon: 'pricetag-outline',
      route: '/(tabs)/discounts',
    },
    {
      id: 'reviews',
      title: 'Reviews',
      icon: 'star-outline',
      route: '/(tabs)/reviews',
    },
    {
      id: 'analytics',
      title: 'Analytics',
      icon: 'analytics-outline',
      route: '/(tabs)/analytics',
    },
    {
      id: 'pages',
      title: 'Pages',
      icon: 'document-outline',
      route: '/(tabs)/pages',
    },
    {
      id: 'booking',
      title: 'Booking',
      icon: 'calendar-outline',
      route: '/(tabs)/booking',
    },
    {
      id: 'marketing',
      title: 'Marketing',
      icon: 'megaphone-outline',
      route: '/(tabs)/marketing',
    },
  ];

  const handleMenuPress = (item: MenuItem) => {
    router.push(item.route as any);
    if (width <= 768) {
      setSidebarVisible(false);
    }
  };

  const isActiveRoute = (route: string) => {
    if (route === '/(tabs)') {
      return pathname === '/(tabs)' || pathname === '/';
    }
    return pathname.includes(route.replace('/(tabs)', ''));
  };

  const renderMenuItem = (item: MenuItem) => (
    <TouchableOpacity
      key={item.id}
      style={[
        styles.menuItem,
        isActiveRoute(item.route) && styles.activeMenuItem
      ]}
      onPress={() => handleMenuPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.menuItemContent}>
        <Ionicons
          name={item.icon as any}
          size={20}
          color={isActiveRoute(item.route) ? '#1f2937' : '#6b7280'}
          style={styles.menuIcon}
        />
        <Text style={[
          styles.menuText,
          isActiveRoute(item.route) && styles.activeMenuText
        ]}>
          {item.title}
        </Text>
      </View>
      <View style={styles.menuRight}>
        {item.badge && (
          <View style={[styles.badge, { backgroundColor: item.badgeColor }]}>
            <Text style={styles.badgeText}>{item.badge}</Text>
          </View>
        )}
        {item.hasSubmenu && (
          <Ionicons
            name="chevron-forward"
            size={16}
            color="#9ca3af"
          />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.layout}>
        {/* Sidebar */}
        <View style={[styles.sidebar, !sidebarVisible && styles.sidebarHidden]}>
          {/* Header */}
          <View style={styles.sidebarHeader}>
            <TouchableOpacity style={styles.menuButton}>
              <Ionicons name="menu" size={24} color="#374151" />
            </TouchableOpacity>
            <Text style={styles.appTitle}>M-Duka</Text>
          </View>

          {/* User Info */}
          <View style={styles.userInfo}>
            <View style={styles.userAvatar}>
              <Ionicons name="storefront" size={20} color="#667eea" />
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{userInfo?.name || 'Loading...'}</Text>
              <Text style={styles.userEmail}>{userInfo?.email || ''}</Text>
            </View>
            <TouchableOpacity>
              <Ionicons name="chevron-down" size={16} color="#9ca3af" />
            </TouchableOpacity>
          </View>

          {/* Menu */}
          <ScrollView style={styles.menuContainer} showsVerticalScrollIndicator={false}>
            {menuItems.map(renderMenuItem)}
            
            <Text style={styles.sectionTitle}>Apps</Text>
            {appsMenuItems.map(renderMenuItem)}
          </ScrollView>
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          {/* Mobile Header */}
          {width <= 768 && (
            <View style={styles.mobileHeader}>
              <TouchableOpacity
                style={styles.mobileMenuButton}
                onPress={() => setSidebarVisible(!sidebarVisible)}
              >
                <Ionicons name="menu" size={24} color="#374151" />
              </TouchableOpacity>
              <Text style={styles.mobileTitle}>M-Duka</Text>
            </View>
          )}
          
          {/* Page Content */}
          <View style={styles.pageContent}>
            {children}
          </View>
        </View>

        {/* Overlay for mobile */}
        {width <= 768 && sidebarVisible && (
          <TouchableOpacity
            style={styles.overlay}
            onPress={() => setSidebarVisible(false)}
            activeOpacity={1}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  layout: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    width: SIDEBAR_WIDTH,
    backgroundColor: 'white',
    borderRightWidth: 1,
    borderRightColor: '#e5e7eb',
    zIndex: 1000,
  },
  sidebarHidden: {
    position: 'absolute',
    left: -SIDEBAR_WIDTH,
    height: '100%',
  },
  sidebarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  menuButton: {
    marginRight: 12,
  },
  appTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1f2937',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f9fafb',
    marginHorizontal: 12,
    marginVertical: 8,
    borderRadius: 8,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#e0e7ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
  },
  userEmail: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  menuContainer: {
    flex: 1,
    paddingHorizontal: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 12,
    marginVertical: 2,
    borderRadius: 8,
  },
  activeMenuItem: {
    backgroundColor: '#f3f4f6',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    marginRight: 12,
  },
  menuText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  activeMenuText: {
    color: '#1f2937',
    fontWeight: '600',
  },
  menuRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#9ca3af',
    marginTop: 24,
    marginBottom: 8,
    marginLeft: 12,
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  mobileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  mobileMenuButton: {
    marginRight: 12,
  },
  mobileTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1f2937',
  },
  pageContent: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 999,
  },
});
