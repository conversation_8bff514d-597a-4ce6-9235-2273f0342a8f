{"name": "m-duka", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build": "eas build", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit": "eas submit", "clear": "expo start --clear"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-picker/picker": "^2.11.0", "@supabase/supabase-js": "^2.50.0", "expo": "~53.0.10", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-notifications": "^0.31.3", "expo-router": "^5.0.7", "expo-status-bar": "~2.2.3", "glob": "^11.0.2", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "5.4.0", "react-native-share": "^12.0.11", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}