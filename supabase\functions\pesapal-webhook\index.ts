import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { crypto } from "https://deno.land/std@0.168.0/crypto/mod.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-pesapal-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface PesapalWebhookEvent {
  OrderTrackingId: string
  OrderMerchantReference: string
  OrderNotificationType: number
  OrderNotificationMessage: string
  
  // IPN (Instant Payment Notification) specific fields
  pesapal_transaction_tracking_id?: string
  pesapal_merchant_reference?: string
  pesapal_notification_type?: number
  pesapal_notification_message?: string
  
  // Payment status
  payment_status?: string
  payment_method?: string
  amount?: number
  currency?: string
}

// Map Pesapal notification types to readable statuses
const getPaymentStatus = (notificationType: number): string => {
  switch (notificationType) {
    case 1:
      return 'COMPLETED'
    case 2:
      return 'PENDING'
    case 3:
      return 'INVALID'
    case 4:
      return 'FAILED'
    default:
      return 'UNKNOWN'
  }
}

// Map plan names to database identifiers
const mapPlanName = (productCode: string): string => {
  switch (productCode) {
    case 'ff4ab78a-b437-4901-baa0-736b41efcf32':
      return 'basic'
    case '2186b965-2fb5-44da-952c-3dd6704270cc':
      return 'premium'
    case '77d32354-4ba7-4a21-9f62-04272852aca8':
      return 'business'
    default:
      return 'basic'
  }
}

// Verify Pesapal webhook signature (if available)
const verifyWebhookSignature = async (
  payload: string,
  signature: string,
  secret: string
): Promise<boolean> => {
  if (!signature || !secret) {
    return true // Skip verification if no signature or secret provided
  }

  try {
    const encoder = new TextEncoder()
    const keyData = encoder.encode(secret)
    const messageData = encoder.encode(payload)
    
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    )
    
    const signatureBuffer = await crypto.subtle.sign('HMAC', cryptoKey, messageData)
    const expectedSignature = Array.from(new Uint8Array(signatureBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
    
    return signature === expectedSignature
  } catch (error) {
    console.error('Signature verification error:', error)
    return false
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get request body and headers
    const body = await req.text()
    const signature = req.headers.get('x-pesapal-signature') || ''
    const pesapalSecret = Deno.env.get('PESAPAL_WEBHOOK_SECRET') || ''

    console.log('Received webhook:', {
      body: body.substring(0, 500), // Log first 500 chars
      signature: signature ? 'present' : 'missing',
      hasSecret: !!pesapalSecret
    })

    // Verify webhook signature if available
    if (pesapalSecret && signature) {
      const isValid = await verifyWebhookSignature(body, signature, pesapalSecret)
      if (!isValid) {
        console.error('Invalid webhook signature')
        return new Response(
          JSON.stringify({ error: 'Invalid signature' }),
          { 
            status: 401, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }
    }

    // Parse webhook data
    let webhookData: PesapalWebhookEvent
    
    try {
      // Try to parse as JSON first
      webhookData = JSON.parse(body)
    } catch {
      // If JSON parsing fails, try to parse as URL-encoded form data
      const formData = new URLSearchParams(body)
      webhookData = {
        OrderTrackingId: formData.get('OrderTrackingId') || '',
        OrderMerchantReference: formData.get('OrderMerchantReference') || '',
        OrderNotificationType: parseInt(formData.get('OrderNotificationType') || '0'),
        OrderNotificationMessage: formData.get('OrderNotificationMessage') || '',
        pesapal_transaction_tracking_id: formData.get('pesapal_transaction_tracking_id') || undefined,
        pesapal_merchant_reference: formData.get('pesapal_merchant_reference') || undefined,
        pesapal_notification_type: parseInt(formData.get('pesapal_notification_type') || '0') || undefined,
        pesapal_notification_message: formData.get('pesapal_notification_message') || undefined,
        payment_status: formData.get('payment_status') || undefined,
        payment_method: formData.get('payment_method') || undefined,
        amount: parseFloat(formData.get('amount') || '0') || undefined,
        currency: formData.get('currency') || undefined,
      }
    }

    const trackingId = webhookData.OrderTrackingId || webhookData.pesapal_transaction_tracking_id || ''
    const merchantRef = webhookData.OrderMerchantReference || webhookData.pesapal_merchant_reference || ''
    const notificationType = webhookData.OrderNotificationType || webhookData.pesapal_notification_type || 0
    const paymentStatus = getPaymentStatus(notificationType)

    console.log('Parsed webhook data:', {
      trackingId,
      merchantRef,
      notificationType,
      paymentStatus
    })

    // Log webhook event
    const { error: logError } = await supabase
      .from('webhook_events')
      .insert({
        event_type: 'pesapal_ipn',
        pesapal_tracking_id: trackingId,
        merchant_reference: merchantRef,
        payment_status: paymentStatus,
        payment_method: webhookData.payment_method,
        amount: webhookData.amount,
        currency: webhookData.currency,
        raw_data: webhookData,
        processed: false
      })

    if (logError) {
      console.error('Error logging webhook event:', logError)
    }

    // Process payment completion
    if (paymentStatus === 'COMPLETED' && merchantRef) {
      console.log('Processing completed payment for merchant reference:', merchantRef)

      // Extract product code from merchant reference if it follows a pattern
      // You might need to adjust this based on how you structure your merchant references
      let productCode = ''
      let userEmail = ''
      
      // Assuming merchant reference format: "user_email|product_code" or similar
      if (merchantRef.includes('|')) {
        const parts = merchantRef.split('|')
        userEmail = parts[0]
        productCode = parts[1]
      } else {
        // If no specific format, you might need to look up the order by tracking ID
        console.log('Merchant reference format not recognized, might need order lookup')
      }

      if (userEmail && productCode) {
        // Find user by email
        const { data: userData, error: userError } = await supabase.auth.admin.listUsers()
        
        if (userError) {
          console.error('Error fetching users:', userError)
        } else {
          const user = userData.users.find(u => u.email === userEmail)
          
          if (user) {
            // Find the subscription plan
            const { data: planData, error: planError } = await supabase
              .from('subscription_plans')
              .select('*')
              .eq('pesapal_product_code', productCode)
              .single()

            if (planError) {
              console.error('Error finding plan:', planError)
            } else if (planData) {
              // Create or update user subscription
              const subscriptionData = {
                user_id: user.id,
                plan_id: planData.id,
                status: 'active',
                current_period_start: new Date().toISOString(),
                current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
                payment_method: 'pesapal',
                pesapal_tracking_id: trackingId,
                pesapal_merchant_reference: merchantRef,
                pesapal_product_code: productCode,
                billing_cycle: 'monthly'
              }

              // Check if subscription already exists
              const { data: existingSubscription } = await supabase
                .from('user_subscriptions')
                .select('*')
                .eq('user_id', user.id)
                .eq('pesapal_tracking_id', trackingId)
                .single()

              if (existingSubscription) {
                // Update existing subscription
                const { error: updateError } = await supabase
                  .from('user_subscriptions')
                  .update({
                    status: 'active',
                    pesapal_tracking_id: trackingId,
                    updated_at: new Date().toISOString()
                  })
                  .eq('id', existingSubscription.id)

                if (updateError) {
                  console.error('Error updating subscription:', updateError)
                } else {
                  console.log('Subscription updated successfully')
                }
              } else {
                // Create new subscription
                const { error: insertError } = await supabase
                  .from('user_subscriptions')
                  .insert(subscriptionData)

                if (insertError) {
                  console.error('Error creating subscription:', insertError)
                } else {
                  console.log('Subscription created successfully')
                }
              }

              // Mark webhook as processed
              await supabase
                .from('webhook_events')
                .update({ processed: true })
                .eq('pesapal_tracking_id', trackingId)
            }
          } else {
            console.error('User not found for email:', userEmail)
          }
        }
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Webhook processed successfully',
        status: paymentStatus 
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Webhook processing error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
