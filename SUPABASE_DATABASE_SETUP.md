# 🗄️ M-Duka Supabase Database Setup Guide

## 📋 Prerequisites

1. **Supabase Account**: Sign up at [supabase.com](https://supabase.com)
2. **Supabase Project**: Create a new project for M-Duka
3. **Database Access**: Access to Supabase SQL Editor

## 🚀 Quick Setup (Recommended)

### Step 1: Run the Complete Deployment Script

1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Create a new query
4. Copy and paste the contents of `supabase/deploy_database.sql`
5. Click **Run** to execute the script

This will create:
- ✅ All tables with proper relationships
- ✅ Row Level Security (RLS) policies
- ✅ Indexes for performance
- ✅ Subscription plans data
- ✅ Basic configuration

### Step 2: Verify Setup

Run this query to verify everything was created:

```sql
-- Check if all tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'subscription_plans',
    'user_subscriptions', 
    'stores',
    'products',
    'orders',
    'customers'
);

-- Check subscription plans
SELECT name, price_monthly FROM subscription_plans ORDER BY sort_order;
```

## 🔧 Manual Setup (Advanced)

If you prefer to run migrations step by step:

### Step 1: Initial Schema
```sql
-- Run: supabase/migrations/001_initial_schema.sql
```

### Step 2: RLS Policies
```sql
-- Run: supabase/migrations/002_rls_policies.sql
```

### Step 3: Functions & Triggers
```sql
-- Run: supabase/migrations/003_functions_triggers.sql
```

### Step 4: Seed Data
```sql
-- Run: supabase/migrations/004_seed_data.sql
```

## 🔒 Security Configuration

### Enable RLS on All Tables

The scripts automatically enable Row Level Security, but verify with:

```sql
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND rowsecurity = true;
```

## 📊 Database Schema Overview

### Core Tables

1. **subscription_plans** - Available subscription tiers
2. **user_subscriptions** - User subscription records
3. **stores** - Store information and settings
4. **products** - Product catalog
5. **orders** - Customer orders
6. **customers** - Customer information
7. **order_items** - Order line items

### Supporting Tables

8. **product_categories** - Product categorization
9. **webhook_events** - Payment webhook logs
10. **analytics_events** - User behavior tracking

## 🔑 Environment Variables

Make sure these are set in your Supabase project:

```env
# Already configured in your .env file
EXPO_PUBLIC_SUPABASE_URL=https://vjwcnvdykoizguxoetgi.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

## 🧪 Testing the Setup

### 1. Test Authentication
```sql
-- This should work after user signup
SELECT auth.uid();
```

### 2. Test Store Creation
```sql
-- Insert a test store (replace user_id with actual auth.uid())
INSERT INTO stores (user_id, name, description) 
VALUES (auth.uid(), 'Test Store', 'My test store');
```

### 3. Test Product Creation
```sql
-- Insert a test product
INSERT INTO products (store_id, name, price) 
VALUES (
    (SELECT id FROM stores WHERE user_id = auth.uid() LIMIT 1),
    'Test Product',
    10000.00
);
```

## 📈 Performance Optimization

### Indexes Created

The setup includes these performance indexes:

- `idx_stores_user_id` - Fast store lookup by user
- `idx_products_store_id` - Fast product lookup by store
- `idx_orders_store_id` - Fast order lookup by store
- `idx_customers_store_id` - Fast customer lookup by store

## 🚨 Troubleshooting

### Common Issues

1. **RLS Blocking Queries**
   - Check if user is authenticated: `SELECT auth.uid()`
   - Verify RLS policies allow the operation

2. **Foreign Key Violations**
   - Ensure referenced records exist
   - Check user owns the referenced store/product

3. **Permission Denied**
   - User might not own the resource
   - Check RLS policies are correctly configured

### Debug Queries

```sql
-- Check current user
SELECT auth.uid(), auth.role();

-- Check user's stores
SELECT * FROM stores WHERE user_id = auth.uid();

-- Check RLS policies
SELECT * FROM pg_policies WHERE schemaname = 'public';
```

## ✅ Verification Checklist

- [ ] All tables created successfully
- [ ] RLS enabled on all tables
- [ ] Subscription plans inserted
- [ ] Indexes created for performance
- [ ] Test user can create store
- [ ] Test user can create products
- [ ] Test customer can place orders
- [ ] RLS policies working correctly

## 🎉 Success!

Your M-Duka database is now ready for production! 

**Next Steps:**
1. Test the authentication flow in your app
2. Create a test store and products
3. Place a test order
4. Monitor performance in Supabase dashboard

## 📞 Support

If you encounter issues:

1. Check Supabase logs in the dashboard
2. Verify environment variables are correct
3. Test queries in SQL Editor
4. Check RLS policies are working

**Database is ready for M-Duka production deployment!** 🚀
