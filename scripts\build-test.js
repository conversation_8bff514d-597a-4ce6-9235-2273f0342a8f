#!/usr/bin/env node

/**
 * Production Build Test Script for M-Duka
 * Tests the build process and validates the output
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting M-Duka Production Build Test...\n');

// Test 1: Environment Variables
console.log('1️⃣ Testing Environment Variables...');
const requiredEnvVars = [
  'EXPO_PUBLIC_SUPABASE_URL',
  'EXPO_PUBLIC_SUPABASE_ANON_KEY',
  'EXPO_PUBLIC_APP_NAME',
  'EXPO_PUBLIC_BASE_URL'
];

let envErrors = [];
requiredEnvVars.forEach(envVar => {
  if (!process.env[envVar]) {
    envErrors.push(`❌ Missing: ${envVar}`);
  } else {
    console.log(`✅ Found: ${envVar}`);
  }
});

if (envErrors.length > 0) {
  console.log('\n❌ Environment Variable Errors:');
  envErrors.forEach(error => console.log(error));
  console.log('\n💡 Make sure to set these in your .env file or Vercel dashboard');
} else {
  console.log('✅ All environment variables found\n');
}

// Test 2: Dependencies
console.log('2️⃣ Testing Dependencies...');
try {
  console.log('Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies installed successfully\n');
} catch (error) {
  console.log('❌ Dependency installation failed');
  process.exit(1);
}

// Test 3: TypeScript Check
console.log('3️⃣ Running TypeScript Check...');
try {
  execSync('npx tsc --noEmit', { stdio: 'inherit' });
  console.log('✅ TypeScript check passed\n');
} catch (error) {
  console.log('❌ TypeScript check failed');
  console.log('💡 Fix TypeScript errors before deploying\n');
}

// Test 4: Build Process
console.log('4️⃣ Testing Build Process...');
try {
  console.log('Building for production...');
  execSync('npm run vercel-build', { stdio: 'inherit' });
  console.log('✅ Build completed successfully\n');
} catch (error) {
  console.log('❌ Build failed');
  process.exit(1);
}

// Test 5: Build Output Validation
console.log('5️⃣ Validating Build Output...');
const distPath = path.join(process.cwd(), 'dist');

if (!fs.existsSync(distPath)) {
  console.log('❌ dist/ directory not found');
  process.exit(1);
}

const requiredFiles = [
  'index.html',
  '_expo/static/js/web',
  '_expo/static/css'
];

let missingFiles = [];
requiredFiles.forEach(file => {
  const filePath = path.join(distPath, file);
  if (!fs.existsSync(filePath)) {
    missingFiles.push(`❌ Missing: ${file}`);
  } else {
    console.log(`✅ Found: ${file}`);
  }
});

if (missingFiles.length > 0) {
  console.log('\n❌ Build Output Errors:');
  missingFiles.forEach(error => console.log(error));
  process.exit(1);
} else {
  console.log('✅ All required build files found\n');
}

// Test 6: Bundle Size Check
console.log('6️⃣ Checking Bundle Size...');
try {
  const statsPath = path.join(distPath, '_expo/static/js/web');
  if (fs.existsSync(statsPath)) {
    const files = fs.readdirSync(statsPath);
    const jsFiles = files.filter(file => file.endsWith('.js'));
    
    let totalSize = 0;
    jsFiles.forEach(file => {
      const filePath = path.join(statsPath, file);
      const stats = fs.statSync(filePath);
      totalSize += stats.size;
      console.log(`📦 ${file}: ${(stats.size / 1024).toFixed(2)} KB`);
    });
    
    const totalMB = (totalSize / 1024 / 1024).toFixed(2);
    console.log(`📊 Total JS Bundle Size: ${totalMB} MB`);
    
    if (totalSize > 5 * 1024 * 1024) { // 5MB
      console.log('⚠️  Bundle size is large (>5MB). Consider code splitting.');
    } else {
      console.log('✅ Bundle size is acceptable\n');
    }
  }
} catch (error) {
  console.log('⚠️  Could not analyze bundle size\n');
}

// Test 7: HTML Validation
console.log('7️⃣ Validating HTML Output...');
try {
  const htmlPath = path.join(distPath, 'index.html');
  const htmlContent = fs.readFileSync(htmlPath, 'utf8');
  
  const requiredTags = [
    '<title>',
    '<meta name="description"',
    '<meta name="viewport"',
    '<link rel="manifest"'
  ];
  
  let missingTags = [];
  requiredTags.forEach(tag => {
    if (!htmlContent.includes(tag)) {
      missingTags.push(`❌ Missing: ${tag}`);
    } else {
      console.log(`✅ Found: ${tag}`);
    }
  });
  
  if (missingTags.length > 0) {
    console.log('\n⚠️  HTML Validation Warnings:');
    missingTags.forEach(warning => console.log(warning));
  } else {
    console.log('✅ HTML validation passed\n');
  }
} catch (error) {
  console.log('⚠️  Could not validate HTML\n');
}

// Summary
console.log('🎉 Build Test Summary:');
console.log('✅ Environment variables configured');
console.log('✅ Dependencies installed');
console.log('✅ Build process completed');
console.log('✅ Build output validated');
console.log('✅ Ready for Vercel deployment!');

console.log('\n🚀 Next Steps:');
console.log('1. Push your code to GitHub');
console.log('2. Connect your repository to Vercel');
console.log('3. Set environment variables in Vercel dashboard');
console.log('4. Deploy!');

console.log('\n📚 Deployment Guide: ./VERCEL_DEPLOYMENT.md');
