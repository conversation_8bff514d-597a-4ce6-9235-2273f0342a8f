import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, StyleSheet, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { supabase, testSupabaseConnection, isSupabaseConfigured } from '../../lib/supabase';
import { signInWithEmail, isValidEmail } from '../../lib/auth';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);

  const handleSignIn = async () => {
    console.log('handleSignIn called');

    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    // Validate email format
    if (!isValidEmail(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    setLoading(true);
    try {
      console.log('Attempting to sign in with:', email);
      console.log('Supabase URL:', process.env.EXPO_PUBLIC_SUPABASE_URL);
      console.log('Supabase configured:', isSupabaseConfigured());

      // Test Supabase connection first
      if (!isSupabaseConfigured()) {
        Alert.alert('Configuration Error', 'Supabase is not properly configured. Please check your environment variables.');
        setLoading(false);
        return;
      }

      const connectionTest = await testSupabaseConnection();
      if (!connectionTest.success) {
        Alert.alert('Connection Error', `Failed to connect to Supabase: ${connectionTest.error}`);
        setLoading(false);
        return;
      }

      const result = await signInWithEmail(email, password);

      if (!result.success) {
        console.error('Sign in error:', result.error);
        Alert.alert('Sign In Error', result.error || 'Unknown error occurred');
        setLoading(false);
        return;
      }

      const { data } = result;

      console.log('Sign in successful:', data);

      // Check if user has completed organization setup
      if (data.user) {
        console.log('Login successful, checking store setup for user:', data.user.id);
        
        // Add a small delay to ensure auth state is properly set
        setTimeout(async () => {
          try {
            const { data: store, error: storeError } = await supabase
              .from('stores')
              .select('id, name')
              .eq('user_id', data.user.id)
              .single();

            if (storeError) {
              console.log('Store query error or no store found:', storeError.message);
              
              // If it's a "no rows" error, user needs to complete setup
              if (storeError.code === 'PGRST116') {
                console.log('No store found, redirecting to setup');
                router.replace('/organization-setup');
              } else {
                // Other database errors, log and still go to setup as safeguard
                console.error('Database error checking store:', storeError);
                router.replace('/organization-setup');
              }
            } else if (store) {
              console.log('Store found:', store.name, '- redirecting to dashboard');
              router.replace('/(tabs)');
            } else {
              console.log('No store data returned, going to setup');
              router.replace('/organization-setup');
            }
          } catch (checkError) {
            console.error('Error checking store setup:', checkError);
            router.replace('/organization-setup');
          }
        }, 500);
      } else {
        console.error('Login succeeded but no user data received');
        Alert.alert('Error', 'Login succeeded but user data is missing. Please try again.');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setTestingConnection(true);
    try {
      console.log('Testing Supabase connection...');
      const result = await testSupabaseConnection();

      if (result.success) {
        Alert.alert(
          'Connection Successful! ✅',
          'Supabase is properly connected and ready to use.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Connection Failed ❌',
          `Error: ${result.error}\n\nPlease check your Supabase configuration.`,
          [{ text: 'OK' }]
        );
      }
    } catch (error: any) {
      Alert.alert(
        'Connection Test Error',
        `Unexpected error: ${error.message}`,
        [{ text: 'OK' }]
      );
    } finally {
      setTestingConnection(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.logoContainer}>
                <View style={styles.logoBackground}>
                  <Ionicons name="storefront" size={40} color="#667eea" />
                </View>
              </View>
              <Text style={styles.appName}>M-Duka</Text>
              <Text style={styles.welcomeText}>Welcome Back!</Text>
              <Text style={styles.subtitle}>
                Sign in to manage your digital store
              </Text>
            </View>

            {/* Form */}
            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <Ionicons name="mail-outline" size={20} color="#6b7280" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Email address"
                  placeholderTextColor="#9ca3af"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete="email"
                  testID="email-input"
                />
              </View>

              <View style={styles.inputContainer}>
                <Ionicons name="lock-closed-outline" size={20} color="#6b7280" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Password"
                  placeholderTextColor="#9ca3af"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoComplete="password"
                  testID="password-input"
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeIcon}
                >
                  <Ionicons
                    name={showPassword ? "eye-outline" : "eye-off-outline"}
                    size={20}
                    color="#6b7280"
                  />
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={[styles.authButton, styles.buttonGradient, loading && styles.buttonDisabled]}
                onPress={() => {
                  console.log('Login button pressed! Loading state:', loading);
                  console.log('Form data:', { email, password: '***' });
                  handleSignIn();
                }}
                disabled={loading}
                testID="signin-button"
                activeOpacity={0.8}
              >
                {loading ? (
                  <View style={styles.loadingContainer}>
                    <Text style={styles.buttonText}>Signing in...</Text>
                  </View>
                ) : (
                  <Text style={styles.buttonText}>Sign In</Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.switchButton}
                onPress={() => router.push('/auth/signup')}
                testID="goto-signup"
              >
                <Text style={styles.switchText}>
                  Don't have an account? {' '}
                  <Text style={styles.switchTextBold}>Sign Up</Text>
                </Text>
              </TouchableOpacity>

              {/* Test Connection Button */}
              <TouchableOpacity
                style={styles.testButton}
                onPress={handleTestConnection}
                disabled={testingConnection}
                activeOpacity={0.8}
              >
                <Text style={styles.testButtonText}>
                  {testingConnection ? '🔄 Testing Connection...' : '🔧 Test Supabase Connection'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.forgotPasswordButton}
                onPress={() => {
                  Alert.alert(
                    'Reset Password',
                    'Enter your email address to reset your password',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Send Reset',
                        onPress: async () => {
                          if (!email) {
                            Alert.alert('Error', 'Please enter your email address first');
                            return;
                          }
                          try {
                            const { error } = await supabase.auth.resetPasswordForEmail(email);
                            if (error) {
                              Alert.alert('Error', error.message);
                            } else {
                              Alert.alert('Success', 'Password reset email sent!');
                            }
                          } catch (error) {
                            Alert.alert('Error', 'Failed to send reset email');
                          }
                        }
                      }
                    ]
                  );
                }}
              >
                <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoContainer: {
    marginBottom: 20,
  },
  logoBackground: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  appName: {
    fontSize: 32,
    fontWeight: '800',
    color: 'white',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: '600',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1f2937',
    paddingVertical: 12,
  },
  eyeIcon: {
    padding: 4,
  },
  authButton: {
    borderRadius: 12,
    marginTop: 8,
    marginBottom: 16,
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  buttonGradient: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    backgroundColor: '#667eea',
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  switchButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchText: {
    fontSize: 14,
    color: '#6b7280',
  },
  switchTextBold: {
    fontWeight: '600',
    color: '#667eea',
  },
  testButton: {
    marginTop: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
    alignItems: 'center',
  },
  testButtonText: {
    fontSize: 14,
    color: '#6c757d',
    fontWeight: '500',
  },
  forgotPasswordButton: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: '#667eea',
    textDecorationLine: 'underline',
  },
});
