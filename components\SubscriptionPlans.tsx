import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert, Linking } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { supabase, SubscriptionPlan } from '../lib/supabase';

interface SubscriptionPlansProps {
  onPlanSelected?: (planId: string) => void;
  selectedPlanId?: string;
  showTrialOption?: boolean;
  showPurchaseButtons?: boolean;
}

export default function SubscriptionPlans({ 
  onPlanSelected, 
  selectedPlanId, 
  showTrialOption = true,
  showPurchaseButtons = true
}: SubscriptionPlansProps) {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price_monthly', { ascending: true });

      if (error) throw error;
      setPlans(data || []);
    } catch (error) {
      console.error('Error fetching plans:', error);
      Alert.alert('Error', 'Failed to load subscription plans');
    } finally {
      setLoading(false);
    }
  };

  const getPrice = (plan: SubscriptionPlan) => {
    if (billingCycle === 'yearly' && plan.price_yearly) {
      return plan.price_yearly;
    }
    return plan.price_monthly;
  };
  const getSavingsText = (plan: SubscriptionPlan) => {
    if (plan.price_yearly && billingCycle === 'yearly') {
      const monthlyTotal = plan.price_monthly * 12;
      const savings = monthlyTotal - plan.price_yearly;
      const percentage = Math.round((savings / monthlyTotal) * 100);
      return `Save ${percentage}%`;
    }
    return null;
  };

  const handlePurchase = async (plan: SubscriptionPlan) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        Alert.alert('Error', 'Please log in to purchase a plan');
        return;
      }

      // Create a pending subscription record
      const { error: insertError } = await supabase
        .from('user_subscriptions')
        .insert({
          user_id: user.id,
          plan_id: plan.id,
          status: 'pending',
          billing_cycle: billingCycle,
          pesapal_product_code: plan.pesapal_product_code,
          pesapal_merchant_reference: `${user.email}|${plan.pesapal_product_code}`
        });

      if (insertError) {
        console.error('Error creating pending subscription:', insertError);
      }

      // Open Pesapal payment URL
      const supported = await Linking.canOpenURL(plan.pesapal_product_url);
      if (supported) {
        await Linking.openURL(plan.pesapal_product_url);
      } else {
        Alert.alert(
          'Payment Link',
          `Please visit: ${plan.pesapal_product_url}`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error handling purchase:', error);
      Alert.alert('Error', 'Failed to initiate purchase');
    }
  };

  const renderPlanCard = (plan: SubscriptionPlan, isPopular = false) => {
    const isSelected = selectedPlanId === plan.id;
    const price = getPrice(plan);
    const savings = getSavingsText(plan);

    return (
      <TouchableOpacity
        key={plan.id}
        style={[
          styles.planCard,
          isSelected && styles.selectedPlan,
          isPopular && styles.popularPlan
        ]}
        onPress={() => onPlanSelected?.(plan.id)}
      >
        {isPopular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularBadgeText}>Most Popular</Text>
          </View>
        )}

        <View style={styles.planHeader}>
          <Text style={[styles.planName, isSelected && styles.selectedText]}>
            {plan.name}
          </Text>
          <View style={styles.priceContainer}>
            <Text style={[styles.price, isSelected && styles.selectedText]}>
              ${price.toFixed(2)}
            </Text>
            <Text style={[styles.pricePeriod, isSelected && styles.selectedText]}>
              /{billingCycle === 'monthly' ? 'month' : 'year'}
            </Text>
          </View>
          {savings && (
            <Text style={styles.savingsText}>{savings}</Text>
          )}
        </View>

        <View style={styles.planFeatures}>
          {plan.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Ionicons 
                name="checkmark-circle" 
                size={16} 
                color={isSelected ? '#667eea' : '#10b981'} 
              />
              <Text style={[styles.featureText, isSelected && styles.selectedText]}>
                {feature}
              </Text>
            </View>
          ))}
          
          {plan.max_products && plan.max_products > 0 && (
            <View style={styles.featureItem}>
              <Ionicons 
                name="cube" 
                size={16} 
                color={isSelected ? '#667eea' : '#6b7280'} 
              />
              <Text style={[styles.featureText, isSelected && styles.selectedText]}>
                Up to {plan.max_products} products
              </Text>
            </View>
          )}

          {plan.max_products === -1 && (
            <View style={styles.featureItem}>
              <Ionicons 
                name="infinite" 
                size={16} 
                color={isSelected ? '#667eea' : '#6b7280'} 
              />
              <Text style={[styles.featureText, isSelected && styles.selectedText]}>
                Unlimited products
              </Text>
            </View>
          )}

          {plan.max_storage_gb && (
            <View style={styles.featureItem}>
              <Ionicons 
                name="cloud" 
                size={16} 
                color={isSelected ? '#667eea' : '#6b7280'} 
              />
              <Text style={[styles.featureText, isSelected && styles.selectedText]}>
                {plan.max_storage_gb}GB storage
              </Text>
            </View>
          )}        </View>

        {showPurchaseButtons && (
          <TouchableOpacity 
            style={[styles.purchaseButton, isSelected && styles.selectedPurchaseButton]}
            onPress={() => handlePurchase(plan)}
          >
            <Text style={[styles.purchaseButtonText, isSelected && styles.selectedPurchaseButtonText]}>
              Choose Plan
            </Text>
          </TouchableOpacity>
        )}

        {isSelected && !showPurchaseButtons && (
          <View style={styles.selectedIndicator}>
            <Ionicons name="checkmark-circle" size={24} color="#667eea" />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading plans...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Choose Your Plan</Text>
      <Text style={styles.subtitle}>
        Select the perfect plan for your business needs
      </Text>

      {/* Billing Cycle Toggle */}
      <View style={styles.billingToggle}>
        <TouchableOpacity
          style={[
            styles.toggleButton,
            billingCycle === 'monthly' && styles.toggleActive
          ]}
          onPress={() => setBillingCycle('monthly')}
        >
          <Text style={[
            styles.toggleText,
            billingCycle === 'monthly' && styles.toggleActiveText
          ]}>
            Monthly
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.toggleButton,
            billingCycle === 'yearly' && styles.toggleActive
          ]}
          onPress={() => setBillingCycle('yearly')}
        >
          <Text style={[
            styles.toggleText,
            billingCycle === 'yearly' && styles.toggleActiveText
          ]}>
            Yearly
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.plansContainer}
      >
        {plans.map((plan, index) => 
          renderPlanCard(plan, plan.name === 'Standard')
        )}
      </ScrollView>

      {showTrialOption && (
        <View style={styles.trialContainer}>
          <Text style={styles.trialText}>
            🎉 Start with a 14-day free trial on any plan!
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6b7280',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    marginBottom: 24,
    marginHorizontal: 24,
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  toggleActive: {
    backgroundColor: '#667eea',
  },
  toggleText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
  },
  toggleActiveText: {
    color: 'white',
  },
  plansContainer: {
    paddingHorizontal: 16,
  },
  planCard: {
    width: 280,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 8,
    borderWidth: 2,
    borderColor: '#e5e7eb',
    position: 'relative',
  },
  selectedPlan: {
    borderColor: '#667eea',
    backgroundColor: '#f8faff',
  },
  popularPlan: {
    borderColor: '#f59e0b',
  },
  popularBadge: {
    position: 'absolute',
    top: -10,
    left: 20,
    backgroundColor: '#f59e0b',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  planHeader: {
    marginBottom: 20,
  },
  planName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1f2937',
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 4,
  },
  price: {
    fontSize: 36,
    fontWeight: '800',
    color: '#1f2937',
  },
  pricePeriod: {
    fontSize: 16,
    color: '#6b7280',
    marginLeft: 4,
  },
  savingsText: {
    fontSize: 12,
    color: '#10b981',
    fontWeight: '600',
  },
  selectedText: {
    color: '#667eea',
  },
  planFeatures: {
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 8,
    flex: 1,
  },  selectedIndicator: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  purchaseButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 8,
  },
  selectedPurchaseButton: {
    backgroundColor: '#667eea',
  },
  purchaseButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  selectedPurchaseButtonText: {
    color: 'white',
  },
  trialContainer: {
    backgroundColor: '#ecfdf5',
    marginHorizontal: 24,
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#10b981',
  },
  trialText: {
    fontSize: 14,
    color: '#065f46',
    textAlign: 'center',
    fontWeight: '500',
  },
});
