# 🚀 M-Duka Vercel Deployment Guide

## 📋 Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **GitHub Repository**: Push your code to GitHub
3. **Supabase Project**: Ensure your Supabase project is configured

## 🔧 Environment Variables Setup

### Required Environment Variables in Vercel:

1. Go to your Vercel project dashboard
2. Navigate to **Settings** → **Environment Variables**
3. Add the following variables:

```env
EXPO_PUBLIC_SUPABASE_URL=https://vjwcnvdykoizguxoetgi.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
EXPO_PUBLIC_APP_NAME=M-Duka
EXPO_PUBLIC_BASE_URL=https://your-domain.vercel.app
```

### Environment Variable Names for Vercel Secrets:
- `expo_public_supabase_url`
- `expo_public_supabase_anon_key`
- `expo_public_app_name`
- `expo_public_base_url`

## 🚀 Deployment Steps

### Method 1: GitHub Integration (Recommended)

1. **Connect Repository**:
   - Go to [vercel.com/new](https://vercel.com/new)
   - Import your GitHub repository
   - Select the M-Duka project

2. **Configure Build Settings**:
   - **Framework Preset**: Other
   - **Build Command**: `npm run vercel-build`
   - **Output Directory**: `dist`
   - **Install Command**: `npm install`

3. **Deploy**:
   - Click "Deploy"
   - Wait for build to complete
   - Your app will be live at `https://your-project.vercel.app`

### Method 2: Vercel CLI

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy from project root
vercel

# Follow the prompts:
# - Set up and deploy? Y
# - Which scope? (select your account)
# - Link to existing project? N
# - Project name: m-duka
# - Directory: ./
```

## 🔧 Build Configuration

The project is configured with:

- **Static Export**: Optimized for Vercel's static hosting
- **Metro Bundler**: For React Native Web compatibility
- **Asset Optimization**: Automatic compression and caching
- **Security Headers**: XSS protection, content security policies

## 📱 Domain Configuration

### Custom Domain Setup:

1. Go to Vercel project dashboard
2. Navigate to **Settings** → **Domains**
3. Add your custom domain (e.g., `mduka.app`)
4. Configure DNS records as instructed

### Recommended Domain Structure:
- **Production**: `mduka.app` or `app.mduka.com`
- **Staging**: `staging.mduka.app`
- **Development**: `dev.mduka.app`

## 🔒 Security Optimizations

The deployment includes:

- **Content Security Policy**: Prevents XSS attacks
- **HTTPS Enforcement**: Automatic SSL certificates
- **Asset Caching**: 1-year cache for static assets
- **Security Headers**: OWASP recommended headers

## 📊 Performance Optimizations

- **Static Generation**: Pre-built HTML for faster loading
- **Asset Compression**: Automatic Gzip/Brotli compression
- **CDN Distribution**: Global edge network
- **Image Optimization**: Automatic WebP conversion
- **Bundle Splitting**: Optimized JavaScript chunks

## 🔍 Monitoring & Analytics

### Vercel Analytics:
1. Enable in project settings
2. Add analytics script to app
3. Monitor performance metrics

### Error Tracking:
- Console errors are logged in Vercel Functions
- Set up Sentry or similar for production error tracking

## 🧪 Testing Deployment

### Pre-deployment Checklist:
- [ ] All environment variables configured
- [ ] Supabase connection tested
- [ ] Build completes successfully
- [ ] All routes work correctly
- [ ] Mobile responsiveness verified
- [ ] Authentication flow tested

### Post-deployment Testing:
```bash
# Test build locally
npm run build-web
npm run preview

# Test production URL
curl -I https://your-domain.vercel.app
```

## 🔄 Continuous Deployment

### Automatic Deployments:
- **Production**: Deploys from `main` branch
- **Preview**: Deploys from pull requests
- **Development**: Deploys from `develop` branch

### Branch Configuration:
```json
{
  "git": {
    "deploymentEnabled": {
      "main": true,
      "develop": true
    }
  }
}
```

## 🛠️ Troubleshooting

### Common Issues:

1. **Build Fails**:
   - Check Node.js version (use 18.x)
   - Verify all dependencies are installed
   - Check for TypeScript errors

2. **Environment Variables Not Working**:
   - Ensure variables start with `EXPO_PUBLIC_`
   - Redeploy after adding variables
   - Check variable names match exactly

3. **Routing Issues**:
   - Verify `vercel.json` configuration
   - Check Expo Router setup
   - Test all routes manually

4. **Supabase Connection Issues**:
   - Verify CORS settings in Supabase
   - Check API keys are correct
   - Test connection in production

## 📞 Support

- **Vercel Docs**: [vercel.com/docs](https://vercel.com/docs)
- **Expo Docs**: [docs.expo.dev](https://docs.expo.dev)
- **Supabase Docs**: [supabase.com/docs](https://supabase.com/docs)

## 🎉 Success!

Your M-Duka app should now be live and accessible worldwide through Vercel's global CDN!

**Production URL**: `https://your-domain.vercel.app`
