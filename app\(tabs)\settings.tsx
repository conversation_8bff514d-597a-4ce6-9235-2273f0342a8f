import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { supabase } from '../../lib/supabase';

const { width } = Dimensions.get('window');

interface UserInfo {
  name: string;
  email: string;
}

export default function Settings() {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [activeSection, setActiveSection] = useState('billing');

  useEffect(() => {
    loadUserInfo();
  }, []);

  const loadUserInfo = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        // Get store info for display name
        const { data: store } = await supabase
          .from('stores')
          .select('name')
          .eq('user_id', user.id)
          .single();

        setUserInfo({
          name: store?.name || 'Store Owner',
          email: user.email || '<EMAIL>'
        });
      }
    } catch (error) {
      console.error('Error loading user info:', error);
    }
  };

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await supabase.auth.signOut();
            router.replace('/auth/login');
          },
        },
      ]
    );
  };

  const handleBackToDashboard = () => {
    router.back();
  };

  const menuItems = [
    { id: 'store', title: 'Store', icon: 'storefront-outline' },
    { id: 'general', title: 'General', icon: 'settings-outline' },
    { id: 'payments', title: 'Payments', icon: 'card-outline' },
    { id: 'checkout', title: 'Checkout', icon: 'bag-outline' },
    { id: 'delivery', title: 'Delivery', icon: 'bicycle-outline' },
    { id: 'whatsapp', title: 'WhatsApp Business', icon: 'logo-whatsapp' },
    { id: 'workflow', title: 'Workflow', icon: 'git-branch-outline' },
    { id: 'domains', title: 'Domains', icon: 'globe-outline' },
    { id: 'membership', title: 'Membership', icon: 'people-outline' },
    { id: 'seo', title: 'SEO and tracking', icon: 'trending-up-outline' },
    { id: 'organization', title: 'Organization', icon: 'business-outline' },
    { id: 'details', title: 'Details', icon: 'document-text-outline' },
    { id: 'billing', title: 'Billing', icon: 'receipt-outline' },
    { id: 'staff', title: 'Staff', icon: 'people-circle-outline' },
    { id: 'files', title: 'Files', icon: 'folder-outline' },
    { id: 'integrations', title: 'Integrations', icon: 'link-outline' },
    { id: 'affiliate', title: 'Affiliate Program', icon: 'share-outline' },
  ];

  const renderSidebar = () => (
    <View style={styles.sidebar}>
      {/* Header */}
      <View style={styles.sidebarHeader}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackToDashboard}
        >
          <Ionicons name="arrow-back" size={20} color="#374151" />
          <Text style={styles.backText}>Return to dashboard</Text>
        </TouchableOpacity>
        <Text style={styles.sidebarTitle}>Settings ({userInfo?.name || 'Loading...'})</Text>
      </View>

      {/* Menu Items */}
      <ScrollView style={styles.menuContainer} showsVerticalScrollIndicator={false}>
        {menuItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.menuItem,
              activeSection === item.id && styles.activeMenuItem
            ]}
            onPress={() => setActiveSection(item.id)}
          >
            <View style={styles.menuItemContent}>
              <Ionicons
                name={item.icon as any}
                size={18}
                color={activeSection === item.id ? '#1f2937' : '#6b7280'}
                style={styles.menuIcon}
              />
              <Text style={[
                styles.menuText,
                activeSection === item.id && styles.activeMenuText
              ]}>
                {item.title}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderBillingContent = () => (
    <View style={styles.mainContent}>
      {/* Header */}
      <View style={styles.contentHeader}>
        <View style={styles.titleRow}>
          <Text style={styles.contentTitle}>Billing</Text>
          <Ionicons name="help-circle-outline" size={20} color="#9ca3af" />
        </View>
      </View>

      <ScrollView style={styles.contentScroll} showsVerticalScrollIndicator={false}>
        {/* Subscription Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Subscription</Text>

          <View style={styles.subscriptionCard}>
            <View style={styles.planHeader}>
              <Text style={styles.planName}>Basic Plan</Text>
            </View>

            <View style={styles.priceRow}>
              <Text style={styles.price}>$ 0.00</Text>
              <Text style={styles.priceUnit}>USD / month</Text>
            </View>

            <View style={styles.billingInfo}>
              <Text style={styles.billingLabel}>Current billing cycle</Text>
              <Text style={styles.billingValue}>01 May 2025 - 01 Jun 2025</Text>
            </View>

            <View style={styles.billingInfo}>
              <Text style={styles.billingLabel}>Payment method</Text>
              <Text style={styles.billingValue}>VISA ending in 2608 (9/2027)</Text>
              <Text style={styles.billingSubtext}>Billing ID: rak_01ag7z4JoMXG</Text>
            </View>
          </View>
        </View>

        {/* Quota Section */}
        <View style={styles.section}>
          <View style={styles.sectionTitleRow}>
            <Text style={styles.sectionTitle}>Quota</Text>
            <Ionicons name="help-circle-outline" size={16} color="#9ca3af" />
          </View>

          <View style={styles.quotaList}>
            <View style={styles.quotaItem}>
              <View style={[styles.quotaIndicator, { backgroundColor: '#10b981' }]} />
              <Text style={styles.quotaText}>Stores</Text>
            </View>
            <View style={styles.quotaItem}>
              <View style={[styles.quotaIndicator, { backgroundColor: '#10b981' }]} />
              <Text style={styles.quotaText}>Staff</Text>
            </View>
            <View style={styles.quotaItem}>
              <View style={[styles.quotaIndicator, { backgroundColor: '#10b981' }]} />
              <Text style={styles.quotaText}>Images</Text>
            </View>
            <View style={styles.quotaItem}>
              <View style={[styles.quotaIndicator, { backgroundColor: '#ef4444' }]} />
              <Text style={styles.quotaText}>Automated WhatsApp</Text>
            </View>
            <View style={styles.quotaItem}>
              <View style={[styles.quotaIndicator, { backgroundColor: '#ef4444' }]} />
              <Text style={styles.quotaText}>Email</Text>
            </View>
          </View>
        </View>

        {/* Add-ons Section */}
        <View style={styles.section}>
          <View style={styles.sectionTitleRow}>
            <Text style={styles.sectionTitle}>Add-ons</Text>
            <Ionicons name="help-circle-outline" size={16} color="#9ca3af" />
          </View>

          <Text style={styles.addOnDescription}>
            Unable to go beyond the free quota. Additional usage will be billed in the next invoice.
          </Text>

          <View style={styles.addOnItem}>
            <Text style={styles.addOnTitle}>Extra automated WhatsApp</Text>
            <Text style={styles.addOnPrice}>Free for 1,000 $0.05 per extra message</Text>
          </View>

          <View style={styles.addOnItem}>
            <Text style={styles.addOnTitle}>Extra email</Text>
          </View>
        </View>

        {/* Support Message */}
        <View style={styles.supportSection}>
          <View style={styles.supportHeader}>
            <View style={styles.avatarContainer}>
              <Text style={styles.avatarText}>MS</Text>
            </View>
            <View style={styles.supportInfo}>
              <Text style={styles.supportName}>M-Duka Support 👋</Text>
              <Text style={styles.supportMessage}>
                Welcome to M-Duka! Your store is set up and ready to go. If you need any help managing your products, orders, or settings, feel free to reach out to our support team.
              </Text>
              <View style={styles.featureList}>
                <Text style={styles.featureItem}>• Add and manage products</Text>
                <Text style={styles.featureItem}>• Track orders and customers</Text>
                <Text style={styles.featureItem}>• Configure payment methods</Text>
                <Text style={styles.featureItem}>• Set up delivery options</Text>
              </View>
              <Text style={styles.supportFooter}>
                Happy selling! 🚀
              </Text>
              <Text style={styles.supportTime}>Support Team • 5m ago</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.layout}>
        {renderSidebar()}
        {renderBillingContent()}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  layout: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    width: width > 768 ? 280 : width * 0.8,
    backgroundColor: 'white',
    borderRightWidth: 1,
    borderRightColor: '#e5e7eb',
  },
  sidebarHeader: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
  },
  backText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  sidebarTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  menuContainer: {
    flex: 1,
    paddingHorizontal: 12,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 12,
    marginVertical: 2,
    borderRadius: 8,
  },
  activeMenuItem: {
    backgroundColor: '#f3f4f6',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    marginRight: 12,
  },
  menuText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  activeMenuText: {
    color: '#1f2937',
    fontWeight: '600',
  },

  mainContent: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  contentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contentTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1f2937',
    marginRight: 8,
  },

  contentScroll: {
    flex: 1,
    padding: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  sectionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  subscriptionCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  planName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },

  priceRow: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 20,
  },
  price: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1f2937',
    marginRight: 8,
  },
  priceUnit: {
    fontSize: 14,
    color: '#6b7280',
  },
  billingInfo: {
    marginBottom: 12,
  },
  billingLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  billingValue: {
    fontSize: 14,
    color: '#1f2937',
    fontWeight: '500',
  },
  billingSubtext: {
    fontSize: 12,
    color: '#9ca3af',
    marginTop: 2,
  },
  quotaList: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  quotaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  quotaIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  quotaText: {
    fontSize: 14,
    color: '#374151',
  },
  addOnDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 16,
    lineHeight: 20,
  },
  addOnItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  addOnTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  addOnPrice: {
    fontSize: 12,
    color: '#6b7280',
  },
  supportSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  supportHeader: {
    flexDirection: 'row',
  },
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  supportInfo: {
    flex: 1,
  },
  supportName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  supportMessage: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginBottom: 12,
  },
  featureList: {
    marginBottom: 12,
  },
  featureItem: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginBottom: 4,
  },
  supportFooter: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '600',
    marginBottom: 8,
  },
  supportTime: {
    fontSize: 12,
    color: '#9ca3af',
  },
});
