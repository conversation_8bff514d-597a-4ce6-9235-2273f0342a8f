# Polar Webhook Integration Setup Guide

## Prerequisites
1. Supabase CLI installed
2. Polar account with webhook endpoint configured
3. Environment variables set in Supabase

## Setup Steps

### 1. Apply Database Schema
```bash
# Run the Polar schema updates
psql -h [your-supabase-db-url] -U postgres -d postgres -f supabase-polar-schema.sql
```

### 2. Set Environment Variables in Supabase
Go to your Supabase project settings > Edge Functions and add:
- `POLAR_WEBHOOK_SECRET`: Your webhook secret from Polar
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Your service role key

### 3. Deploy the Edge Function
```bash
# Install Supabase CLI if not already installed
npm install -g supabase

# Login to Supabase
supabase login

# Deploy the function
supabase functions deploy polar-webhook
```

### 4. Configure Polar Webhook
In your Polar dashboard:
1. Go to Webhooks settings
2. Add new webhook endpoint: `https://[your-supabase-project].functions.supabase.co/polar-webhook`
3. Select events: `subscription.created`, `subscription.updated`, `subscription.cancelled`
4. Set the webhook secret (same as `POLAR_WEBHOOK_SECRET`)

## Testing

### Local Testing with Supabase CLI
```bash
# Start local development
supabase functions serve polar-webhook --env-file .env.local

# Test with curl (see test-webhook.sh)
```

### Test with Sample Webhook Event
```bash
curl -X POST http://localhost:54321/functions/v1/polar-webhook \
  -H "Content-Type: application/json" \
  -H "polar-webhook-signature: sha256=YOUR_SIGNATURE" \
  -d @test-subscription-created.json
```

## Monitoring
- Check webhook events in the `webhook_events` table
- Monitor function logs in Supabase dashboard
- Set up alerts for failed webhook processing

## Troubleshooting
1. Verify webhook signature is correct
2. Check environment variables are set
3. Ensure user exists with the email from Polar
4. Verify plan names match between Polar and your database
