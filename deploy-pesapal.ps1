#!/usr/bin/env pwsh

# M-Duka Pesapal Deployment Script
# This script deploys the Pesapal webhook edge function to Supabase

Write-Host "🚀 Deploying M-Duka Pesapal Integration..." -ForegroundColor Green

# Check if Supabase CLI is installed
if (!(Get-Command "supabase" -ErrorAction SilentlyContinue)) {
    Write-Error "Supabase CLI is not installed. Please install it first."
    exit 1
}

# Deploy the edge function
Write-Host "📡 Deploying Pesapal webhook function..." -ForegroundColor Yellow
try {
    supabase functions deploy pesapal-webhook --project-ref vjwcnvdykoizguxoetgi
    Write-Host "✅ Edge function deployed successfully!" -ForegroundColor Green
} catch {
    Write-Error "Failed to deploy edge function: $_"
    exit 1
}

# Set environment variables
Write-Host "🔧 Setting environment variables..." -ForegroundColor Yellow
$secretsToSet = @(
    @{name="PESAPAL_WEBHOOK_SECRET"; description="Pesapal webhook secret for signature verification"}
)

foreach ($secret in $secretsToSet) {
    Write-Host "Please set the secret: $($secret.name)" -ForegroundColor Cyan
    Write-Host "Description: $($secret.description)" -ForegroundColor Gray
    $secretValue = Read-Host "Enter value for $($secret.name)" -AsSecureString
    $plainSecret = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($secretValue))
    
    try {
        supabase secrets set "$($secret.name)=$plainSecret" --project-ref vjwcnvdykoizguxoetgi
        Write-Host "✅ Secret $($secret.name) set successfully!" -ForegroundColor Green
    } catch {
        Write-Warning "Failed to set secret $($secret.name): $_"
    }
}

Write-Host ""
Write-Host "🎉 Deployment completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Copy the webhook URL from Supabase Functions dashboard"
Write-Host "2. Configure this URL in your Pesapal merchant dashboard"
Write-Host "3. Test the webhook by making a test payment"
Write-Host ""
Write-Host "🔗 Webhook URL will be:" -ForegroundColor Cyan
Write-Host "https://vjwcnvdykoizguxoetgi.supabase.co/functions/v1/pesapal-webhook"
Write-Host ""
