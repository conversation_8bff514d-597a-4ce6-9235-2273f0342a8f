import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions, TouchableOpacity, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../../lib/supabase';

const { width } = Dimensions.get('window');

interface AnalyticsData {
  totalSales: number;
  totalOrders: number;
  totalCustomers: number;
  averageOrderValue: number;
  recentOrders: any[];
  topProducts: any[];
  salesTrend: any[];
}

export default function Analytics() {
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    totalSales: 0,
    totalOrders: 0,
    totalCustomers: 0,
    averageOrderValue: 0,
    recentOrders: [],
    topProducts: [],
    salesTrend: []
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadAnalytics();
  }, []);

  const loadAnalytics = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: store } = await supabase
        .from('stores')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (!store) return;

      // Load orders data
      const { data: orders } = await supabase
        .from('orders')
        .select(`
          *,
          items:order_items(*)
        `)
        .eq('store_id', store.id);

      // Load products data
      const { data: products } = await supabase
        .from('products')
        .select('*')
        .eq('store_id', store.id);

      if (orders) {
        const totalSales = orders.reduce((sum, order) => sum + order.total_amount, 0);
        const totalOrders = orders.length;
        const uniqueCustomers = new Set(orders.map(order => order.customer_phone)).size;
        const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

        // Get recent orders (last 5)
        const recentOrders = orders
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, 5);

        // Calculate top products
        const productSales: { [key: string]: { name: string; quantity: number; revenue: number } } = {};
        
        orders.forEach(order => {
          if (order.items) {
            order.items.forEach((item: any) => {
              if (!productSales[item.product_id]) {
                productSales[item.product_id] = {
                  name: item.product_name,
                  quantity: 0,
                  revenue: 0
                };
              }
              productSales[item.product_id].quantity += item.quantity;
              productSales[item.product_id].revenue += item.price * item.quantity;
            });
          }
        });

        const topProducts = Object.values(productSales)
          .sort((a, b) => b.revenue - a.revenue)
          .slice(0, 5);

        setAnalytics({
          totalSales,
          totalOrders,
          totalCustomers: uniqueCustomers,
          averageOrderValue,
          recentOrders,
          topProducts,
          salesTrend: []
        });
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadAnalytics();
  };

  const MetricCard = ({ title, value, icon, color, subtitle }: {
    title: string;
    value: string;
    icon: string;
    color: string;
    subtitle?: string;
  }) => (
    <View style={[styles.metricCard, { width: (width - 72) / 2 }]}>
      <View style={styles.metricHeader}>
        <View style={[styles.metricIcon, { backgroundColor: color }]}>
          <Ionicons name={icon as any} size={20} color="white" />
        </View>
      </View>
      <Text style={styles.metricValue}>{value}</Text>
      <Text style={styles.metricTitle}>{title}</Text>
      {subtitle && <Text style={styles.metricSubtitle}>{subtitle}</Text>}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading analytics...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.pageHeader}>
        <Text style={styles.pageTitle}>Analytics</Text>
        <Text style={styles.pageSubtitle}>Get insights into your store performance and customer behavior.</Text>
      </View>

      <ScrollView 
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.content}>
          {/* Key Metrics */}
          <View style={styles.metricsGrid}>
            <MetricCard
              title="Total Sales"
              value={`KSh ${analytics.totalSales.toLocaleString()}`}
              icon="cash-outline"
              color="#10b981"
            />
            <MetricCard
              title="Total Orders"
              value={analytics.totalOrders.toString()}
              icon="receipt-outline"
              color="#3b82f6"
            />
            <MetricCard
              title="Customers"
              value={analytics.totalCustomers.toString()}
              icon="people-outline"
              color="#8b5cf6"
            />
            <MetricCard
              title="Avg. Order Value"
              value={`KSh ${Math.round(analytics.averageOrderValue).toLocaleString()}`}
              icon="trending-up-outline"
              color="#f59e0b"
            />
          </View>

          {/* Top Products */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Top Selling Products</Text>
            <View style={styles.topProductsCard}>
              {analytics.topProducts.length > 0 ? (
                analytics.topProducts.map((product, index) => (
                  <View key={index} style={styles.productItem}>
                    <View style={styles.productRank}>
                      <Text style={styles.rankText}>{index + 1}</Text>
                    </View>
                    <View style={styles.productInfo}>
                      <Text style={styles.productName}>{product.name}</Text>
                      <Text style={styles.productStats}>
                        {product.quantity} sold • KSh {product.revenue.toLocaleString()}
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <View style={styles.emptyState}>
                  <Ionicons name="cube-outline" size={48} color="#9ca3af" />
                  <Text style={styles.emptyText}>No sales data yet</Text>
                </View>
              )}
            </View>
          </View>

          {/* Recent Orders */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Orders</Text>
            <View style={styles.ordersCard}>
              {analytics.recentOrders.length > 0 ? (
                analytics.recentOrders.map((order, index) => (
                  <View key={order.id} style={styles.orderItem}>
                    <View style={styles.orderInfo}>
                      <Text style={styles.orderCustomer}>{order.customer_name}</Text>
                      <Text style={styles.orderDate}>
                        {new Date(order.created_at).toLocaleDateString()}
                      </Text>
                    </View>
                    <View style={styles.orderRight}>
                      <Text style={styles.orderAmount}>
                        KSh {order.total_amount.toLocaleString()}
                      </Text>
                      <View style={[styles.statusBadge, { 
                        backgroundColor: order.status === 'delivered' ? '#dcfce7' : 
                                        order.status === 'confirmed' ? '#dbeafe' : '#fef3c7'
                      }]}>
                        <Text style={[styles.statusText, {
                          color: order.status === 'delivered' ? '#10b981' : 
                                 order.status === 'confirmed' ? '#3b82f6' : '#f59e0b'
                        }]}>
                          {order.status}
                        </Text>
                      </View>
                    </View>
                  </View>
                ))
              ) : (
                <View style={styles.emptyState}>
                  <Ionicons name="receipt-outline" size={48} color="#9ca3af" />
                  <Text style={styles.emptyText}>No orders yet</Text>
                </View>
              )}
            </View>
          </View>

          {/* Quick Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.actionsGrid}>
              <TouchableOpacity style={styles.actionCard}>
                <Ionicons name="add-circle-outline" size={32} color="#3b82f6" />
                <Text style={styles.actionText}>Add Product</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionCard}>
                <Ionicons name="share-outline" size={32} color="#10b981" />
                <Text style={styles.actionText}>Share Store</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6b7280',
  },
  pageHeader: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1f2937',
    marginBottom: 4,
  },
  pageSubtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 24,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  metricCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  metricHeader: {
    marginBottom: 12,
  },
  metricIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  metricValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1f2937',
    marginBottom: 4,
  },
  metricTitle: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  metricSubtitle: {
    fontSize: 12,
    color: '#9ca3af',
    marginTop: 2,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  topProductsCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  productRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  rankText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 4,
  },
  productStats: {
    fontSize: 14,
    color: '#6b7280',
  },
  ordersCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  orderInfo: {
    flex: 1,
  },
  orderCustomer: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 4,
  },
  orderDate: {
    fontSize: 14,
    color: '#6b7280',
  },
  orderRight: {
    alignItems: 'flex-end',
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#9ca3af',
    marginTop: 12,
  },
  actionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginTop: 8,
  },
});
