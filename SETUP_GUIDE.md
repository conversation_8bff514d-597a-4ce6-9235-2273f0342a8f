# M-Duka Setup Guide

This guide will help you set up and run the M-Duka mobile eCommerce app.

## 🚀 Quick Start

### 1. Prerequisites

Make sure you have the following installed:
- Node.js (v18 or later)
- npm or yarn
- Expo CLI: `npm install -g @expo/cli`
- Expo Go app on your mobile device

### 2. Project Setup

1. **Navigate to the project directory:**
   ```bash
   cd M-Duka
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` and add your Supabase credentials (see Supabase Setup below).

### 3. Supabase Setup

1. **Create a Supabase project:**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project URL and anon key

2. **Set up the database:**
   - Go to the SQL Editor in your Supabase dashboard
   - Copy and paste the contents of `supabase-schema.sql`
   - Run the SQL to create tables and policies

3. **Configure authentication:**
   - Go to Authentication > Settings
   - Enable email authentication
   - Configure any additional providers if needed

4. **Update environment variables:**
   ```env
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

### 4. Run the App

1. **Start the development server:**
   ```bash
   npm start
   ```

2. **Open on your device:**
   - Scan the QR code with Expo Go (Android) or Camera app (iOS)
   - Or press `a` for Android emulator, `i` for iOS simulator

## 📱 App Features

### For Store Owners

1. **Authentication:**
   - Sign up with email and password
   - Secure login with Supabase Auth

2. **Store Setup:**
   - Create store with unique URL slug
   - Add store description and contact info
   - Configure WhatsApp number for orders

3. **Product Management:**
   - Add products with images, descriptions, and prices
   - Organize products by categories
   - Toggle product availability

4. **Order Management:**
   - View incoming orders in real-time
   - Update order status (pending → confirmed → delivered)
   - Track order history and analytics

5. **Store Sharing:**
   - Share unique store URL
   - Customers can browse without app installation

### For Customers

1. **Browse Stores:**
   - Visit public store URLs
   - Browse products without registration

2. **Place Orders:**
   - Add products to cart
   - Order directly via WhatsApp
   - Automatic order message generation

## 🛠️ Development

### Project Structure

```
M-Duka/
├── app/                    # Expo Router pages
│   ├── (tabs)/            # Main app navigation
│   ├── auth/              # Authentication screens
│   ├── products/          # Product management
│   ├── store/             # Public store pages
│   └── onboarding.tsx     # Store setup
├── components/            # Reusable UI components
├── lib/                   # Utilities and configurations
│   ├── supabase.ts        # Database client
│   ├── utils.ts           # Helper functions
│   └── constants.ts       # App constants
└── assets/                # Images and static files
```

### Key Technologies

- **React Native + Expo**: Cross-platform mobile development
- **Expo Router**: File-based navigation
- **Supabase**: Backend-as-a-Service (auth, database, real-time)
- **NativeWind**: Tailwind CSS for React Native
- **React Native Paper**: UI component library
- **TypeScript**: Type safety and better DX

### Database Schema

The app uses these main tables:

1. **stores**: Store information and settings
2. **products**: Product catalog for each store
3. **orders**: Customer orders and tracking

All tables have Row Level Security (RLS) policies for data protection.

## 🔧 Configuration

### Environment Variables

Create a `.env` file with:

```env
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# App Configuration
EXPO_PUBLIC_APP_NAME=M-Duka
EXPO_PUBLIC_BASE_URL=https://mduka.app
```

### Customization

1. **App Name and Branding:**
   - Update `app.json` for app metadata
   - Replace icons in `assets/` folder
   - Modify colors in `lib/constants.ts`

2. **Features:**
   - Add new product categories in `constants.ts`
   - Customize WhatsApp message templates
   - Add new order statuses if needed

## 📦 Building for Production

### Using EAS Build

1. **Install EAS CLI:**
   ```bash
   npm install -g @expo/eas-cli
   ```

2. **Login to Expo:**
   ```bash
   eas login
   ```

3. **Configure build:**
   ```bash
   eas build:configure
   ```

4. **Build for production:**
   ```bash
   eas build --platform all
   ```

### Manual Build

For local builds, you can use:
```bash
npx expo run:android
npx expo run:ios
```

## 🐛 Troubleshooting

### Common Issues

1. **Metro bundler errors:**
   ```bash
   npx expo start --clear
   ```

2. **Dependency conflicts:**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Supabase connection issues:**
   - Check environment variables
   - Verify Supabase project URL and keys
   - Ensure RLS policies are set up correctly

4. **NativeWind styling issues:**
   - Ensure `nativewind/preset` is in `tailwind.config.js`
   - Check that `global.css` is imported in `_layout.tsx`

### Getting Help

- Check the [Expo documentation](https://docs.expo.dev/)
- Visit [Supabase docs](https://supabase.com/docs)
- Create an issue on the project repository

## 🚀 Deployment

### Web Deployment

The app can be deployed as a web app:

```bash
npx expo export --platform web
```

### App Store Deployment

Use EAS Submit for app store deployment:

```bash
eas submit --platform ios
eas submit --platform android
```

## 📈 Next Steps

After basic setup, consider:

1. **Adding analytics** with Expo Analytics
2. **Implementing push notifications** with Expo Notifications
3. **Adding payment integration** (M-Pesa, Stripe)
4. **Setting up CI/CD** with GitHub Actions
5. **Adding tests** with Jest and React Native Testing Library

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

Happy coding! 🎉
