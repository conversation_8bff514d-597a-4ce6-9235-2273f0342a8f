-- M-Duka Database Schema for Supabase with Pesapal Integration
-- Run this script in your Supabase SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS webhook_events CASCADE;
DROP TABLE IF EXISTS orders CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS stores CASCADE;
DROP TABLE IF EXISTS user_subscriptions CASCADE;
DROP TABLE IF EXISTS subscription_plans CASCADE;

-- Subscription plans table with Pesapal product codes
CREATE TABLE subscription_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHA<PERSON>(50) NOT NULL,
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2),
    max_products INTEGER,
    max_orders INTEGER,
    max_storage_gb INTEGER,
    features JSONB NOT NULL DEFAULT '[]',
    pesapal_product_code VARCHAR(255) NOT NULL,
    pesapal_product_url TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User subscriptions table with Pesapal integration
CREATE TABLE user_subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES subscription_plans(id),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired', 'trial', 'pending')),
    current_period_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    current_period_end TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
    trial_end TIMESTAMP WITH TIME ZONE,
    payment_method VARCHAR(50) DEFAULT 'pesapal',
    pesapal_order_id VARCHAR(255),
    pesapal_tracking_id VARCHAR(255),
    pesapal_merchant_reference VARCHAR(255),
    pesapal_product_code VARCHAR(255),
    billing_cycle VARCHAR(20) DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Webhook events table for logging Pesapal webhooks
CREATE TABLE webhook_events (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    pesapal_order_id VARCHAR(255),
    pesapal_tracking_id VARCHAR(255),
    merchant_reference VARCHAR(255),
    payment_status VARCHAR(50),
    payment_method VARCHAR(50),
    amount DECIMAL(10,2),
    currency VARCHAR(10),
    raw_data JSONB,
    processed BOOLEAN DEFAULT false,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stores table
CREATE TABLE stores (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    logo_url TEXT,
    whatsapp_number VARCHAR(20),
    payment_instructions TEXT,
    business_hours TEXT,
    subscription_id UUID REFERENCES user_subscriptions(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    image_url TEXT,
    category VARCHAR(100),
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Orders table
CREATE TABLE orders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    items JSONB NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'delivered', 'cancelled')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_stores_user_id ON stores(user_id);
CREATE INDEX idx_stores_slug ON stores(slug);
CREATE INDEX idx_stores_subscription_id ON stores(subscription_id);
CREATE INDEX idx_products_store_id ON products(store_id);
CREATE INDEX idx_orders_store_id ON orders(store_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX idx_user_subscriptions_pesapal_order_id ON user_subscriptions(pesapal_order_id);
CREATE INDEX idx_webhook_events_pesapal_order_id ON webhook_events(pesapal_order_id);
CREATE INDEX idx_webhook_events_event_type ON webhook_events(event_type);
CREATE INDEX idx_webhook_events_processed ON webhook_events(processed);

-- Row Level Security (RLS) policies
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

-- Subscription plans policies (public read)
CREATE POLICY "Public can view active subscription plans" ON subscription_plans
    FOR SELECT USING (is_active = true);

-- User subscriptions policies
CREATE POLICY "Users can view their own subscriptions" ON user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own subscriptions" ON user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own subscriptions" ON user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- Webhook events policies (service role only)
CREATE POLICY "Service role can manage webhook events" ON webhook_events
    FOR ALL USING (auth.role() = 'service_role');

-- Stores policies
CREATE POLICY "Users can view their own stores" ON stores
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own stores" ON stores
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own stores" ON stores
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own stores" ON stores
    FOR DELETE USING (auth.uid() = user_id);

-- Public access to stores for customers
CREATE POLICY "Public can view active stores" ON stores
    FOR SELECT USING (is_active = true);

-- Products policies
CREATE POLICY "Store owners can manage their products" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = products.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- Public access to products for customers
CREATE POLICY "Public can view available products" ON products
    FOR SELECT USING (
        is_available = true AND
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = products.store_id 
            AND stores.is_active = true
        )
    );

-- Orders policies
CREATE POLICY "Store owners can view their orders" ON orders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = orders.store_id 
            AND stores.user_id = auth.uid()
        )
    );

CREATE POLICY "Store owners can update their orders" ON orders
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = orders.store_id 
            AND stores.user_id = auth.uid()
        )
    );

-- Public can insert orders (customers placing orders)
CREATE POLICY "Public can place orders" ON orders
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM stores 
            WHERE stores.id = orders.store_id 
            AND stores.is_active = true
        )
    );

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_stores_updated_at BEFORE UPDATE ON stores
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Realtime for tables
ALTER PUBLICATION supabase_realtime ADD TABLE stores;
ALTER PUBLICATION supabase_realtime ADD TABLE products;
ALTER PUBLICATION supabase_realtime ADD TABLE orders;
ALTER PUBLICATION supabase_realtime ADD TABLE user_subscriptions;
ALTER PUBLICATION supabase_realtime ADD TABLE webhook_events;

-- Insert subscription plans with Pesapal product codes and URLs
INSERT INTO subscription_plans (
    name, 
    price_monthly, 
    price_yearly, 
    max_products, 
    max_orders, 
    max_storage_gb, 
    features,
    pesapal_product_code,
    pesapal_product_url
) VALUES
(
    'Basic', 
    9.00, 
    90.00, 
    100, 
    500, 
    5, 
    '["Online Store", "Basic Analytics", "Email Support", "WhatsApp Integration", "Basic Templates"]',
    'ff4ab78a-b437-4901-baa0-736b41efcf32',
    'https://store.pesapal.com/shop/svdp2z-omitechgroupcompanylimited?productCode=ff4ab78a-b437-4901-baa0-736b41efcf32'
),
(
    'Premium', 
    19.00, 
    190.00, 
    500, 
    2000, 
    25, 
    '["Everything in Basic", "Advanced Analytics", "Custom Domain", "Priority Support", "Advanced Templates", "Inventory Management", "Multi-Payment Methods"]',
    '2186b965-2fb5-44da-952c-3dd6704270cc',
    'https://store.pesapal.com/shop/svdp2z-omitechgroupcompanylimited?productCode=2186b965-2fb5-44da-952c-3dd6704270cc'
),
(
    'Business', 
    39.00, 
    390.00, 
    -1, 
    -1, 
    100, 
    '["Everything in Premium", "White-label Solution", "API Access", "Custom Integrations", "24/7 Phone Support", "Advanced Reporting", "Multi-store Management", "Custom Features"]',
    '77d32354-4ba7-4a21-9f62-04272852aca8',
    'https://store.pesapal.com/shop/svdp2z-omitechgroupcompanylimited?productCode=77d32354-4ba7-4a21-9f62-04272852aca8'
);
