import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, ScrollView, Linking } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { supabase, Store } from '../../lib/supabase';
import { formatPhoneNumber, validatePhoneNumber } from '../../lib/utils';

export default function WhatsAppSettings() {
  const [store, setStore] = useState<Store | null>(null);
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    loadStoreInfo();
  }, []);

  const loadStoreInfo = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: storeData, error } = await supabase
        .from('stores')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error loading store:', error);
        return;
      }

      setStore(storeData);
      setWhatsappNumber(storeData.whatsapp_number || '');
    } catch (error) {
      console.error('Error loading store info:', error);
    } finally {
      setInitialLoading(false);
    }
  };

  const updateWhatsAppSettings = async () => {
    if (whatsappNumber && !validatePhoneNumber(whatsappNumber)) {
      Alert.alert('Error', 'Please enter a valid Kenyan phone number (e.g., +254712345678 or 0712345678)');
      return;
    }

    if (!store) return;

    setLoading(true);

    try {
      const formattedNumber = whatsappNumber ? formatPhoneNumber(whatsappNumber) : null;

      const { error } = await supabase
        .from('stores')
        .update({
          whatsapp_number: formattedNumber,
          updated_at: new Date().toISOString(),
        })
        .eq('id', store.id);

      if (error) {
        console.error('Error updating WhatsApp settings:', error);
        Alert.alert('Error', 'Failed to update WhatsApp settings');
        return;
      }

      Alert.alert('Success', 'WhatsApp settings updated successfully!');
      setWhatsappNumber(formattedNumber || '');
    } catch (error) {
      console.error('Error updating WhatsApp settings:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const testWhatsApp = () => {
    if (!whatsappNumber) {
      Alert.alert('Error', 'Please save a WhatsApp number first');
      return;
    }

    const testMessage = `Hello! This is a test message from ${store?.name || 'M-Duka'}. Your WhatsApp integration is working correctly! 🎉`;
    const whatsappUrl = `whatsapp://send?phone=${whatsappNumber}&text=${encodeURIComponent(testMessage)}`;
    
    Linking.openURL(whatsappUrl).catch(() => {
      Alert.alert('Error', 'WhatsApp is not installed on this device');
    });
  };

  if (initialLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 justify-center items-center">
          <Text className="text-gray-600">Loading WhatsApp settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-row items-center px-6 py-4 border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900 ml-4">WhatsApp Settings</Text>
      </View>

      <ScrollView className="flex-1 px-6 py-4">
        <View className="space-y-6">
          {/* WhatsApp Number */}
          <View>
            <Text className="text-gray-700 font-medium mb-2">WhatsApp Number</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
              placeholder="+254712345678 or 0712345678"
              value={whatsappNumber}
              onChangeText={setWhatsappNumber}
              keyboardType="phone-pad"
            />
            <Text className="text-gray-500 text-sm mt-1">
              Customers will use this number to place orders via WhatsApp
            </Text>
          </View>

          {/* Current Status */}
          <View>
            <Text className="text-gray-700 font-medium mb-3">Current Status</Text>
            <View className={`p-4 rounded-lg border ${
              whatsappNumber 
                ? 'bg-green-50 border-green-200' 
                : 'bg-orange-50 border-orange-200'
            }`}>
              <View className="flex-row items-center">
                <Ionicons 
                  name={whatsappNumber ? "checkmark-circle" : "warning"} 
                  size={20} 
                  color={whatsappNumber ? "#10b981" : "#f59e0b"} 
                />
                <Text className={`font-medium ml-2 ${
                  whatsappNumber ? 'text-green-700' : 'text-orange-700'
                }`}>
                  {whatsappNumber ? 'WhatsApp Configured' : 'WhatsApp Not Configured'}
                </Text>
              </View>
              <Text className={`text-sm mt-1 ${
                whatsappNumber ? 'text-green-600' : 'text-orange-600'
              }`}>
                {whatsappNumber 
                  ? 'Customers can place orders via WhatsApp'
                  : 'Add your WhatsApp number to receive orders'
                }
              </Text>
            </View>
          </View>

          {/* Test WhatsApp */}
          {whatsappNumber && (
            <View>
              <Text className="text-gray-700 font-medium mb-3">Test Integration</Text>
              <TouchableOpacity
                className="bg-green-600 rounded-lg py-3 flex-row items-center justify-center"
                onPress={testWhatsApp}
              >
                <Ionicons name="logo-whatsapp" size={20} color="white" />
                <Text className="text-white font-medium ml-2">Send Test Message</Text>
              </TouchableOpacity>
              <Text className="text-gray-500 text-sm mt-2 text-center">
                This will open WhatsApp with a test message
              </Text>
            </View>
          )}

          {/* Update Button */}
          <TouchableOpacity
            className={`bg-blue-600 rounded-lg py-4 mt-8 ${loading ? 'opacity-50' : ''}`}
            onPress={updateWhatsAppSettings}
            disabled={loading}
          >
            <Text className="text-white text-center font-semibold text-lg">
              {loading ? 'Updating...' : 'Update WhatsApp Settings'}
            </Text>
          </TouchableOpacity>

          {/* How it Works */}
          <View className="bg-blue-50 rounded-lg p-4 mt-6">
            <View className="flex-row items-start">
              <Ionicons name="information-circle" size={20} color="#3b82f6" />
              <View className="ml-3 flex-1">
                <Text className="text-blue-800 font-medium mb-2">How WhatsApp Orders Work</Text>
                <Text className="text-blue-700 text-sm">
                  1. Customers browse your store and add items to cart{'\n'}
                  2. They tap "Order via WhatsApp" button{'\n'}
                  3. WhatsApp opens with a pre-filled order message{'\n'}
                  4. Customer sends the message to your WhatsApp{'\n'}
                  5. You receive the order and can confirm with customer
                </Text>
              </View>
            </View>
          </View>

          {/* Tips */}
          <View className="bg-gray-50 rounded-lg p-4">
            <Text className="text-gray-800 font-medium mb-2">💡 Tips for WhatsApp Orders</Text>
            <Text className="text-gray-700 text-sm">
              • Use a business WhatsApp number if possible{'\n'}
              • Respond to orders quickly to maintain customer trust{'\n'}
              • Set up WhatsApp Business for better customer management{'\n'}
              • Create quick replies for common responses{'\n'}
              • Keep your WhatsApp status updated with business hours
            </Text>
          </View>

          {/* Phone Number Formats */}
          <View className="bg-yellow-50 rounded-lg p-4">
            <Text className="text-yellow-800 font-medium mb-2">📱 Supported Phone Formats</Text>
            <Text className="text-yellow-700 text-sm">
              • International: +254712345678{'\n'}
              • Local: 0712345678{'\n'}
              • Without prefix: 712345678{'\n\n'}
              All formats will be automatically converted to international format (+254...)
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
