import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { supabase } from '../lib/supabase';

const { width, height } = Dimensions.get('window');

export default function SplashScreen() {
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.3));
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Start checking auth after animations complete
      checkAuthAndNavigate();
    });
  }, []);

  const checkAuthAndNavigate = async () => {
    try {
      console.log('Checking authentication status...');
      
      // Ensure minimum splash time for better UX
      const minSplashTime = new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check current session
      const sessionPromise = supabase.auth.getSession();
      
      // Wait for both minimum time and session check
      const [sessionResult] = await Promise.all([sessionPromise, minSplashTime]);
      const { data: { session }, error: sessionError } = sessionResult;
      
      if (sessionError) {
        console.error('Session error:', sessionError);
        Alert.alert('Connection Error', 'Unable to check authentication status. Please check your internet connection.');
        router.replace('/auth/login');
        return;
      }
      
      if (session?.user) {
        console.log('User authenticated, checking store setup...');
        
        // User is authenticated, check if they have completed onboarding
        const { data: store, error: storeError } = await supabase
          .from('stores')
          .select('id, name')
          .eq('user_id', session.user.id)
          .single();
        
        if (storeError) {
          console.log('Store query error or no store found:', storeError.message);
          
          // If it's a "no rows" error, user needs to complete setup
          if (storeError.code === 'PGRST116') {
            router.replace('/organization-setup');
          } else {
            // Other database errors, log and go to setup
            console.error('Database error:', storeError);
            Alert.alert('Database Error', 'Unable to load store information. Redirecting to setup.');
            router.replace('/organization-setup');
          }
        } else if (store) {
          console.log('Store found:', store.name, '- going to dashboard');
          // User has completed onboarding, go to dashboard
          router.replace('/(tabs)');
        } else {
          console.log('No store data, going to setup');
          router.replace('/organization-setup');
        }
      } else {
        console.log('No authenticated user, going to login');
        // User is not authenticated, go to login
        router.replace('/auth/login');
      }
    } catch (error) {
      console.error('Unexpected error checking auth status:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please restart the app.');
      // On unexpected error, go to login as fallback
      router.replace('/auth/login');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.container}
    >
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* Logo */}
        <View style={styles.logoContainer}>
          <View style={styles.logoBackground}>
            <Ionicons name="storefront" size={60} color="#667eea" />
          </View>
        </View>

        {/* App Name */}
        <Text style={styles.appName}>M-Duka</Text>
        <Text style={styles.tagline}>Your Digital Store, Simplified</Text>

        {/* Loading indicator */}
        <View style={styles.loadingContainer}>
          <View style={styles.loadingDots}>
            <Animated.View style={[styles.dot, styles.dot1]} />
            <Animated.View style={[styles.dot, styles.dot2]} />
            <Animated.View style={[styles.dot, styles.dot3]} />
          </View>
        </View>

        {/* Version */}
        <Text style={styles.version}>Version 1.0.0</Text>
      </Animated.View>

      {/* Background Pattern */}
      <View style={styles.backgroundPattern}>
        <View style={[styles.circle, styles.circle1]} />
        <View style={[styles.circle, styles.circle2]} />
        <View style={[styles.circle, styles.circle3]} />
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    zIndex: 1,
  },
  logoContainer: {
    marginBottom: 30,
  },
  logoBackground: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  appName: {
    fontSize: 42,
    fontWeight: '800',
    color: 'white',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  tagline: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 60,
    textAlign: 'center',
    fontWeight: '500',
  },
  loadingContainer: {
    marginBottom: 40,
  },
  loadingDots: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'white',
    marginHorizontal: 4,
  },
  dot1: {
    opacity: 0.4,
  },
  dot2: {
    opacity: 0.7,
  },
  dot3: {
    opacity: 1,
  },
  version: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: '500',
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  circle: {
    position: 'absolute',
    borderRadius: 1000,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  circle1: {
    width: 200,
    height: 200,
    top: -100,
    right: -100,
  },
  circle2: {
    width: 150,
    height: 150,
    bottom: -75,
    left: -75,
  },
  circle3: {
    width: 100,
    height: 100,
    top: height * 0.3,
    left: -50,
  },
});
