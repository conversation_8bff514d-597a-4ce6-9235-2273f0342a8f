-- Database Functions and Triggers for M-Duka
-- This file contains utility functions and automated triggers

-- =============================================
-- UTILITY FUNCTIONS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to generate unique store slug
CREATE OR REPLACE FUNCTION generate_store_slug(store_name TEXT)
RETURNS TEXT AS $$
DECLARE
    base_slug TEXT;
    final_slug TEXT;
    counter INTEGER := 0;
BEGIN
    -- Create base slug from store name
    base_slug := lower(regexp_replace(store_name, '[^a-zA-Z0-9]+', '-', 'g'));
    base_slug := trim(both '-' from base_slug);
    
    -- Ensure slug is not empty
    IF base_slug = '' THEN
        base_slug := 'store';
    END IF;
    
    final_slug := base_slug;
    
    -- Check for uniqueness and append counter if needed
    WHILE EXISTS (SELECT 1 FROM stores WHERE slug = final_slug) LOOP
        counter := counter + 1;
        final_slug := base_slug || '-' || counter;
    END LOOP;
    
    RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- Function to generate order number
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    order_num TEXT;
    counter INTEGER := 1;
BEGIN
    LOOP
        order_num := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(counter::TEXT, 4, '0');
        
        IF NOT EXISTS (SELECT 1 FROM orders WHERE order_number = order_num) THEN
            EXIT;
        END IF;
        
        counter := counter + 1;
    END LOOP;
    
    RETURN order_num;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate order total
CREATE OR REPLACE FUNCTION calculate_order_total(order_id_param UUID)
RETURNS DECIMAL AS $$
DECLARE
    total DECIMAL := 0;
BEGIN
    SELECT COALESCE(SUM(total_price), 0)
    INTO total
    FROM order_items
    WHERE order_id = order_id_param;
    
    RETURN total;
END;
$$ LANGUAGE plpgsql;

-- Function to update customer statistics
CREATE OR REPLACE FUNCTION update_customer_stats(customer_id_param UUID)
RETURNS VOID AS $$
DECLARE
    total_orders_count INTEGER;
    total_spent_amount DECIMAL;
    avg_order_value DECIMAL;
    first_order TIMESTAMP WITH TIME ZONE;
    last_order TIMESTAMP WITH TIME ZONE;
BEGIN
    SELECT 
        COUNT(*),
        COALESCE(SUM(total_amount), 0),
        COALESCE(AVG(total_amount), 0),
        MIN(order_date),
        MAX(order_date)
    INTO 
        total_orders_count,
        total_spent_amount,
        avg_order_value,
        first_order,
        last_order
    FROM orders
    WHERE customer_id = customer_id_param
    AND status NOT IN ('cancelled');
    
    UPDATE customers
    SET 
        total_orders = total_orders_count,
        total_spent = total_spent_amount,
        average_order_value = avg_order_value,
        first_order_date = first_order,
        last_order_date = last_order,
        updated_at = NOW()
    WHERE id = customer_id_param;
END;
$$ LANGUAGE plpgsql;

-- Function to update product order count
CREATE OR REPLACE FUNCTION update_product_order_count(product_id_param UUID)
RETURNS VOID AS $$
DECLARE
    order_count INTEGER;
BEGIN
    SELECT COUNT(DISTINCT order_id)
    INTO order_count
    FROM order_items
    WHERE product_id = product_id_param;
    
    UPDATE products
    SET 
        order_count = order_count,
        updated_at = NOW()
    WHERE id = product_id_param;
END;
$$ LANGUAGE plpgsql;

-- Function to check subscription limits
CREATE OR REPLACE FUNCTION check_subscription_limits(user_id_param UUID, limit_type TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_plan RECORD;
    current_usage INTEGER;
    limit_value INTEGER;
BEGIN
    -- Get user's current subscription plan
    SELECT sp.max_products, sp.max_orders, sp.max_storage_gb
    INTO user_plan
    FROM user_subscriptions us
    JOIN subscription_plans sp ON sp.id = us.plan_id
    WHERE us.user_id = user_id_param
    AND us.status = 'active'
    ORDER BY us.created_at DESC
    LIMIT 1;
    
    -- If no active subscription, use trial limits
    IF user_plan IS NULL THEN
        CASE limit_type
            WHEN 'products' THEN limit_value := 10;
            WHEN 'orders' THEN limit_value := 50;
            WHEN 'storage' THEN limit_value := 1;
            ELSE RETURN false;
        END CASE;
    ELSE
        CASE limit_type
            WHEN 'products' THEN limit_value := user_plan.max_products;
            WHEN 'orders' THEN limit_value := user_plan.max_orders;
            WHEN 'storage' THEN limit_value := user_plan.max_storage_gb;
            ELSE RETURN false;
        END CASE;
    END IF;
    
    -- -1 means unlimited
    IF limit_value = -1 THEN
        RETURN true;
    END IF;
    
    -- Get current usage
    CASE limit_type
        WHEN 'products' THEN
            SELECT COUNT(*)
            INTO current_usage
            FROM products p
            JOIN stores s ON s.id = p.store_id
            WHERE s.user_id = user_id_param;
            
        WHEN 'orders' THEN
            SELECT COUNT(*)
            INTO current_usage
            FROM orders o
            JOIN stores s ON s.id = o.store_id
            WHERE s.user_id = user_id_param
            AND o.created_at >= DATE_TRUNC('month', NOW());
            
        WHEN 'storage' THEN
            -- This would need to be implemented based on file storage usage
            current_usage := 0;
    END CASE;
    
    RETURN current_usage < limit_value;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- TRIGGERS
-- =============================================

-- Updated_at triggers for all tables
CREATE TRIGGER update_subscription_plans_updated_at
    BEFORE UPDATE ON subscription_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at
    BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_stores_updated_at
    BEFORE UPDATE ON stores
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_categories_updated_at
    BEFORE UPDATE ON product_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Auto-generate store slug
CREATE OR REPLACE FUNCTION auto_generate_store_slug()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.slug IS NULL OR NEW.slug = '' THEN
        NEW.slug := generate_store_slug(NEW.name);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER auto_generate_store_slug_trigger
    BEFORE INSERT ON stores
    FOR EACH ROW EXECUTE FUNCTION auto_generate_store_slug();

-- Auto-generate order number
CREATE OR REPLACE FUNCTION auto_generate_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER auto_generate_order_number_trigger
    BEFORE INSERT ON orders
    FOR EACH ROW EXECUTE FUNCTION auto_generate_order_number();

-- Update order total when order items change
CREATE OR REPLACE FUNCTION update_order_total_trigger()
RETURNS TRIGGER AS $$
DECLARE
    order_total DECIMAL;
BEGIN
    -- Calculate new total
    IF TG_OP = 'DELETE' THEN
        order_total := calculate_order_total(OLD.order_id);
        UPDATE orders SET total_amount = order_total WHERE id = OLD.order_id;
        RETURN OLD;
    ELSE
        order_total := calculate_order_total(NEW.order_id);
        UPDATE orders SET total_amount = order_total WHERE id = NEW.order_id;
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_order_total_on_items_change
    AFTER INSERT OR UPDATE OR DELETE ON order_items
    FOR EACH ROW EXECUTE FUNCTION update_order_total_trigger();

-- Update customer stats when orders change
CREATE OR REPLACE FUNCTION update_customer_stats_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        IF OLD.customer_id IS NOT NULL THEN
            PERFORM update_customer_stats(OLD.customer_id);
        END IF;
        RETURN OLD;
    ELSE
        IF NEW.customer_id IS NOT NULL THEN
            PERFORM update_customer_stats(NEW.customer_id);
        END IF;
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_customer_stats_on_order_change
    AFTER INSERT OR UPDATE OR DELETE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_customer_stats_trigger();

-- Update product order count when order items change
CREATE OR REPLACE FUNCTION update_product_stats_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        IF OLD.product_id IS NOT NULL THEN
            PERFORM update_product_order_count(OLD.product_id);
        END IF;
        RETURN OLD;
    ELSE
        IF NEW.product_id IS NOT NULL THEN
            PERFORM update_product_order_count(NEW.product_id);
        END IF;
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_product_stats_on_order_items_change
    AFTER INSERT OR UPDATE OR DELETE ON order_items
    FOR EACH ROW EXECUTE FUNCTION update_product_stats_trigger();

-- =============================================
-- SECURITY FUNCTIONS
-- =============================================

-- Function to check if user owns store
CREATE OR REPLACE FUNCTION user_owns_store(store_id_param UUID, user_id_param UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM stores
        WHERE id = store_id_param
        AND user_id = user_id_param
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if store is public
CREATE OR REPLACE FUNCTION store_is_public(store_id_param UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM stores
        WHERE id = store_id_param
        AND is_public = true
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
