{"version": 2, "name": "m-duka", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/assets/(.*)", "headers": {"cache-control": "public, max-age=31536000, immutable"}}, {"src": "/_expo/(.*)", "headers": {"cache-control": "public, max-age=31536000, immutable"}}, {"src": "/static/(.*)", "headers": {"cache-control": "public, max-age=31536000, immutable"}}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "headers": {"cache-control": "public, max-age=31536000, immutable"}}, {"src": "/manifest.json", "headers": {"cache-control": "public, max-age=86400"}}, {"src": "/(.*)", "dest": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "env": {"EXPO_PUBLIC_SUPABASE_URL": "@expo_public_supabase_url", "EXPO_PUBLIC_SUPABASE_ANON_KEY": "@expo_public_supabase_anon_key", "EXPO_PUBLIC_APP_NAME": "@expo_public_app_name", "EXPO_PUBLIC_BASE_URL": "@expo_public_base_url"}, "functions": {"app/**": {"includeFiles": "dist/**"}}}