# 🚀 Supabase Setup Guide for M-Duka

This guide will help you set up Supabase backend for your M-Duka app.

## 📋 Prerequisites

- Supabase account (free tier is sufficient)
- Your M-Duka app with environment variables configured

## 🔧 Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Sign in or create an account
4. Click "New Project"
5. Choose your organization
6. Fill in project details:
   - **Name**: M-Duka
   - **Database Password**: Choose a strong password
   - **Region**: Choose closest to your users
7. Click "Create new project"
8. Wait for the project to be created (2-3 minutes)

## 🗄️ Step 2: Set Up Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Click "New Query"
3. Copy the entire content from `supabase-schema.sql` file
4. Paste it into the SQL editor
5. Click "Run" to execute the script

This will create:
- ✅ Tables: `stores`, `products`, `orders`
- ✅ Row Level Security (RLS) policies
- ✅ Indexes for performance
- ✅ Real-time subscriptions
- ✅ Triggers for automatic timestamps

## 🔑 Step 3: Get API Keys

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://your-project.supabase.co`)
   - **anon public** key (starts with `eyJ...`)

## 🔧 Step 4: Update Environment Variables

Update your `.env` file with the copied values:

```env
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJ...your-anon-key
EXPO_PUBLIC_APP_NAME=M-Duka
EXPO_PUBLIC_BASE_URL=https://mduka.app
```

## 🔐 Step 5: Configure Authentication

1. Go to **Authentication** → **Settings**
2. Under **Site URL**, add your app URLs:
   - `http://localhost:8081` (for development)
   - Your production domain (when deployed)
3. Under **Redirect URLs**, add:
   - `http://localhost:8081/**`
   - Your production domain with wildcard
4. **Email Templates** (optional):
   - Customize signup/login email templates
   - Add your app branding

## 📱 Step 6: Test the Integration

1. Restart your Expo development server:
   ```bash
   npx expo start --clear
   ```

2. Open the app and test:
   - ✅ Sign up with a new account
   - ✅ Complete onboarding (create store)
   - ✅ Add products
   - ✅ View dashboard with real data

## 🔄 Step 7: Enable Real-time (Optional)

Real-time is already configured in the schema, but you can verify:

1. Go to **Database** → **Replication**
2. Make sure these tables are enabled:
   - ✅ `stores`
   - ✅ `products` 
   - ✅ `orders`

## 🛡️ Step 8: Security Verification

Verify Row Level Security is working:

1. Go to **Authentication** → **Users**
2. You should see your test user
3. Go to **Table Editor** → **stores**
4. You should only see stores created by your user

## 📊 Step 9: Monitor Usage

1. Go to **Reports** to monitor:
   - Database usage
   - API requests
   - Authentication events
   - Real-time connections

## 🚀 Step 10: Production Deployment

For production deployment:

1. **Custom Domain** (optional):
   - Go to **Settings** → **Custom Domains**
   - Add your domain for the public store URLs

2. **Environment Variables**:
   - Update `EXPO_PUBLIC_BASE_URL` to your production domain
   - Keep the same Supabase URL and keys

3. **Rate Limiting**:
   - Monitor API usage in Reports
   - Upgrade plan if needed

## 🔧 Troubleshooting

### Common Issues:

**1. "Invalid API key" error:**
- Check that your `.env` file is in the root directory
- Verify the API key is copied correctly
- Restart the Expo development server

**2. "Row Level Security" errors:**
- Make sure you ran the complete SQL schema
- Check that RLS policies are enabled in Table Editor

**3. "Auth session not found":**
- Clear app data/cache
- Sign out and sign in again
- Check authentication settings

**4. Real-time not working:**
- Verify tables are added to replication
- Check network connectivity
- Monitor real-time connections in Reports

### Getting Help:

- Check Supabase documentation: [docs.supabase.com](https://docs.supabase.com)
- Join Supabase Discord community
- Check the M-Duka GitHub issues

## 📈 Next Steps

After successful setup:

1. **Add sample data** through the app interface
2. **Test all features** (products, orders, store management)
3. **Customize** the app for your needs
4. **Deploy** to production when ready

## 🎉 Success!

Your M-Duka app is now fully integrated with Supabase! You have:

- ✅ Real-time database
- ✅ User authentication
- ✅ Secure data access
- ✅ Scalable backend
- ✅ Production-ready infrastructure

The app will now use real data instead of demo data, and all features will be fully functional!
