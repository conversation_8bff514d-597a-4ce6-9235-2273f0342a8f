import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, StyleSheet, KeyboardAvoidingView, Platform, ScrollView, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { supabase } from '../lib/supabase';
import SubscriptionPlans from '../components/SubscriptionPlans';

export default function OrganizationSetup() {
  const [storeName, setStoreName] = useState('');
  const [storeSlug, setStoreSlug] = useState('');
  const [description, setDescription] = useState('');
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [businessHours, setBusinessHours] = useState('Mon-Fri: 10AM-7PM, Sat: 10AM-5PM');
  const [paymentInstructions, setPaymentInstructions] = useState('');
  const [selectedPlanId, setSelectedPlanId] = useState('');
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  
  // Dropdown states
  const [showBusinessHoursModal, setShowBusinessHoursModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('Stripe');

  // Navigation helper function
  const navigateToTabs = () => {
    console.log('navigateToTabs called');
    
    setTimeout(() => {
      try {
        console.log('Attempting navigation to /(tabs)...');
        router.replace('/(tabs)');
        console.log('Navigation initiated successfully');
      } catch (error) {
        console.error('Navigation failed:', error);
        Alert.alert(
          'Navigation Issue',
          'Store created successfully! Please tap the home button or restart the app to access your dashboard.',
          [{ 
            text: 'OK', 
            onPress: () => console.log('User acknowledged navigation issue') 
          }]
        );
      }
    }, 500);
  };

  // Predefined options
  const businessHoursOptions = [
    'Mon-Fri: 8AM-5PM, Sat: 8AM-2PM',
    'Mon-Fri: 9AM-6PM, Sat: 9AM-4PM',
    'Mon-Fri: 10AM-7PM, Sat: 10AM-5PM',
    'Mon-Sat: 8AM-8PM',
    'Mon-Sun: 9AM-9PM',
    'Mon-Sun: 24/7',
    'Custom Hours'
  ];

  const paymentMethods = [
    'M-Pesa',
    'Bank Transfer',
    'Cash on Delivery',
    'PayPal',
    'Stripe',
    'Multiple Methods'
  ];

  const paymentInstructionsTemplates = {
    'M-Pesa': 'M-Pesa: **********, Name: [Your Name]',
    'Bank Transfer': 'Bank: KCB, Account: **********, Name: [Your Name]',
    'Cash on Delivery': 'Cash payment on delivery. Please have exact amount ready.',
    'PayPal': 'PayPal: <EMAIL>',
    'Stripe': 'Card payments accepted through secure checkout',
    'Multiple Methods': 'M-Pesa: **********, Bank: KCB **********, Cash on delivery accepted'
  };

  // Auto-generate slug from store name
  const handleStoreNameChange = (name: string) => {
    setStoreName(name);
    const slug = name
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
    setStoreSlug(slug);
  };

  // Handle business hours selection
  const handleBusinessHoursSelect = (hours: string) => {
    setBusinessHours(hours);
    setShowBusinessHoursModal(false);
  };

  // Handle payment method selection
  const handlePaymentMethodSelect = (method: string) => {
    setSelectedPaymentMethod(method);
    const templates: Record<string, string> = paymentInstructionsTemplates;
    setPaymentInstructions(templates[method] || '');
    setShowPaymentModal(false);
  };

  const validateStep1 = () => {
    if (!storeName.trim()) {
      Alert.alert('Error', 'Please enter your store name');
      return false;
    }
    if (!storeSlug.trim()) {
      Alert.alert('Error', 'Please enter a store URL');
      return false;
    }
    if (storeSlug.length < 3) {
      Alert.alert('Error', 'Store URL must be at least 3 characters');
      return false;
    }
    return true;
  };

  const validateStep2 = () => {
    if (!whatsappNumber.trim()) {
      Alert.alert('Error', 'Please enter your WhatsApp number');
      return false;
    }
    
    if (selectedPaymentMethod && !paymentInstructions.trim()) {
      Alert.alert('Error', 'Please select a payment method or enter payment instructions');
      return false;
    }
    
    return true;
  };

  const validateStep3 = () => {
    if (!selectedPlanId) {
      Alert.alert('Error', 'Please select a subscription plan');
      return false;
    }
    return true;
  };

  const handleNext = () => {
    console.log('handleNext called, currentStep:', currentStep);
    
    if (currentStep === 1) {
      if (validateStep1()) {
        setCurrentStep(2);
      }
    } else if (currentStep === 2) {
      if (validateStep2()) {
        setCurrentStep(3);
      }
    } else if (currentStep === 3) {
      if (validateStep3()) {
        handleCreateStore();
      }
    }
  };

  const createSubscription = async (userId: string, planId: string) => {
    const { data, error } = await supabase
      .from('user_subscriptions')
      .insert({
        user_id: userId,
        plan_id: planId,
        status: 'trial',
        trial_end: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days trial
        current_period_end: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  };

  const handleCreateStore = async () => {
    console.log('handleCreateStore called');
    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        Alert.alert('Error', 'User not authenticated');
        setLoading(false);
        return;
      }

      // Check if slug is available
      const { data: existingStore } = await supabase
        .from('stores')
        .select('id')
        .eq('slug', storeSlug)
        .single();

      if (existingStore) {
        Alert.alert('Error', 'This store URL is already taken. Please choose another.');
        setCurrentStep(1);
        setLoading(false);
        return;
      }

      // Create subscription first
      const subscription = await createSubscription(user.id, selectedPlanId);

      // Create store with subscription
      const { data: storeData, error } = await supabase
        .from('stores')
        .insert({
          user_id: user.id,
          name: storeName.trim(),
          slug: storeSlug.trim(),
          description: description.trim() || null,
          whatsapp_number: whatsappNumber.trim(),
          payment_instructions: paymentInstructions.trim(),
          business_hours: businessHours.trim(),
          subscription_id: subscription.id,
          is_active: true,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating store:', error);
        Alert.alert('Error', error.message || 'Failed to create store. Please try again.');
        setLoading(false);
        return;
      }

      console.log('Store created successfully:', storeData);
      Alert.alert(
        'Success!',
        'Your store has been created successfully with a 14-day free trial. Welcome to M-Duka!',
        [{ 
          text: 'Continue', 
          onPress: () => {
            console.log('Alert continue button pressed, navigating to tabs...');
            navigateToTabs();
          }
        }]
      );
    } catch (error: any) {
      console.error('Unexpected error in handleCreateStore:', error);
      Alert.alert('Error', error.message || 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Store Information</Text>
      <Text style={styles.stepSubtitle}>Let's set up your digital store</Text>

      <View style={styles.inputContainer}>
        <Ionicons name="storefront-outline" size={20} color="#6b7280" style={styles.inputIcon} />
        <TextInput
          style={styles.input}
          placeholder="Store Name (e.g., Tech Haven)"
          placeholderTextColor="#9ca3af"
          value={storeName}
          onChangeText={handleStoreNameChange}
          maxLength={100}
        />
      </View>

      <View style={styles.inputContainer}>
        <Ionicons name="link-outline" size={20} color="#6b7280" style={styles.inputIcon} />
        <TextInput
          style={styles.input}
          placeholder="Store URL (e.g., tech-haven)"
          placeholderTextColor="#9ca3af"
          value={storeSlug}
          onChangeText={setStoreSlug}
          autoCapitalize="none"
          maxLength={50}
        />
      </View>
      <Text style={styles.urlPreview}>
        Your store will be available at: mduka.app/{storeSlug || 'your-store'}
      </Text>

      <View style={styles.inputContainer}>
        <Ionicons name="document-text-outline" size={20} color="#6b7280" style={styles.inputIcon} />
        <TextInput
          style={[styles.input, styles.textArea]}
          placeholder="Store Description (optional)"
          placeholderTextColor="#9ca3af"
          value={description}
          onChangeText={setDescription}
          multiline
          numberOfLines={3}
          maxLength={500}
        />
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Business Details</Text>
      <Text style={styles.stepSubtitle}>How customers can reach and pay you</Text>

      <View style={styles.inputContainer}>
        <Ionicons name="logo-whatsapp" size={20} color="#25D366" style={styles.inputIcon} />
        <TextInput
          style={styles.input}
          placeholder="WhatsApp Number (e.g., +255959511360)"
          placeholderTextColor="#9ca3af"
          value={whatsappNumber}
          onChangeText={setWhatsappNumber}
          keyboardType="phone-pad"
          maxLength={20}
        />
      </View>

      <TouchableOpacity 
        style={styles.dropdownContainer}
        onPress={() => setShowBusinessHoursModal(true)}
      >
        <Ionicons name="time-outline" size={20} color="#6b7280" style={styles.inputIcon} />
        <Text style={[styles.dropdownText, !businessHours && styles.placeholderText]}>
          {businessHours || 'Select Business Hours'}
        </Text>
        <Ionicons name="chevron-down-outline" size={20} color="#6b7280" />
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.dropdownContainer}
        onPress={() => setShowPaymentModal(true)}
      >
        <Ionicons name="card-outline" size={20} color="#6b7280" style={styles.inputIcon} />
        <Text style={[styles.dropdownText, !selectedPaymentMethod && styles.placeholderText]}>
          {selectedPaymentMethod || 'Select Payment Method'}
        </Text>
        <Ionicons name="chevron-down-outline" size={20} color="#6b7280" />
      </TouchableOpacity>

      <View style={styles.paymentInstructionsContainer}>
        <View style={styles.inputContainer}>
          <Ionicons name="information-circle-outline" size={20} color="#6b7280" style={styles.inputIcon} />
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Payment instructions will appear here when you select a method"
            placeholderTextColor="#9ca3af"
            value={paymentInstructions}
            onChangeText={setPaymentInstructions}
            multiline
            numberOfLines={3}
            maxLength={500}
          />
        </View>
        <Text style={styles.helperText}>
          Card payments accepted through secure checkout
        </Text>
      </View>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <SubscriptionPlans 
        onPlanSelected={setSelectedPlanId}
        selectedPlanId={selectedPlanId}
        showTrialOption={true}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <KeyboardAvoidingView 
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <ScrollView 
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.logoContainer}>
                <View style={styles.logoBackground}>
                  <Ionicons name="storefront" size={40} color="#667eea" />
                </View>
              </View>
              <Text style={styles.appName}>M-Duka</Text>
              <Text style={styles.welcomeText}>Setup Your Store</Text>
              
              {/* Progress Indicator */}
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View style={[styles.progressFill, { width: `${(currentStep / 3) * 100}%` }]} />
                </View>
                <Text style={styles.progressText}>Step {currentStep} of 3</Text>
              </View>
            </View>

            {/* Form */}
            <View style={styles.formContainer}>
              {currentStep === 1 && renderStep1()}
              {currentStep === 2 && renderStep2()}
              {currentStep === 3 && renderStep3()}

              <View style={styles.buttonContainer}>
                {currentStep > 1 && (
                  <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => setCurrentStep(currentStep - 1)}
                  >
                    <Text style={styles.backButtonText}>Back</Text>
                  </TouchableOpacity>
                )}

                <TouchableOpacity
                  style={[styles.nextButton, loading && styles.buttonDisabled]}
                  onPress={handleNext}
                  disabled={loading}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={loading ? ['#9ca3af', '#9ca3af'] : ['#667eea', '#764ba2']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.buttonGradient}
                  >
                    <Text style={styles.buttonText}>
                      {loading ? 'Creating Store...' : (currentStep === 3 ? 'Start Free Trial' : 'Next')}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Business Hours Modal */}
        <Modal
          visible={showBusinessHoursModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowBusinessHoursModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Select Business Hours</Text>
              <ScrollView showsVerticalScrollIndicator={false}>
                {businessHoursOptions.map((option, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.optionItem}
                    onPress={() => handleBusinessHoursSelect(option)}
                  >
                    <Text style={styles.optionText}>{option}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowBusinessHoursModal(false)}
              >
                <Text style={styles.closeButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        {/* Payment Method Modal */}
        <Modal
          visible={showPaymentModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowPaymentModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Select Payment Method</Text>
              <ScrollView showsVerticalScrollIndicator={false}>
                {paymentMethods.map((method, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.optionItem}
                    onPress={() => handlePaymentMethodSelect(method)}
                  >
                    <Text style={styles.optionText}>{method}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowPaymentModal(false)}
              >
                <Text style={styles.closeButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoContainer: {
    marginBottom: 20,
  },
  logoBackground: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  appName: {
    fontSize: 32,
    fontWeight: '800',
    color: 'white',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: '600',
    color: 'white',
    marginBottom: 20,
    textAlign: 'center',
  },
  progressContainer: {
    alignItems: 'center',
    width: '100%',
  },
  progressBar: {
    width: 200,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
    minHeight: 500,
  },
  stepContainer: {
    marginBottom: 24,
    flex: 1,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1f2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1f2937',
    paddingVertical: 12,
  },
  textArea: {
    paddingVertical: 12,
    textAlignVertical: 'top',
  },
  urlPreview: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 16,
    marginLeft: 16,
    fontStyle: 'italic',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
  },
  backButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6b7280',
  },
  nextButton: {
    flex: 1,
    marginLeft: 16,
    borderRadius: 12,
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  buttonGradient: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  dropdownText: {
    flex: 1,
    fontSize: 16,
    color: '#1f2937',
  },
  placeholderText: {
    color: '#9ca3af',
  },
  paymentInstructionsContainer: {
    marginTop: 8,
  },
  helperText: {
    fontSize: 12,
    color: '#6b7280',
    fontStyle: 'italic',
    marginLeft: 16,
    marginTop: -8,
    marginBottom: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxHeight: '70%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 20,
    textAlign: 'center',
  },
  optionItem: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  optionText: {
    fontSize: 16,
    color: '#1f2937',
  },
  closeButton: {
    marginTop: 20,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#f3f4f6',
    borderRadius: 10,
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
});