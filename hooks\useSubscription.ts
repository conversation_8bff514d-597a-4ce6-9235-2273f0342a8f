import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import type { UserSubscription, SubscriptionPlan } from '../lib/supabase';

export interface SubscriptionLimits {
  maxProducts: number;
  maxOrders: number;
  maxStorageGb: number;
  features: string[];
  canAddProducts: boolean;
  canProcessOrders: boolean;
  hasAnalytics: boolean;
  hasInventoryManagement: boolean;
  hasPrioritySupport: boolean;
}

export interface UsageStats {
  products: number;
  orders: number;
  storageUsed: number;
}

export function useSubscription() {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [limits, setLimits] = useState<SubscriptionLimits | null>(null);
  const [usage, setUsage] = useState<UsageStats>({ products: 0, orders: 0, storageUsed: 0 });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setLoading(false);
        return;
      }

      // Get subscription data
      const { data: subscriptionData } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          plan:subscription_plans(*)
        `)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      setSubscription(subscriptionData);

      if (subscriptionData?.plan) {
        const plan = subscriptionData.plan as SubscriptionPlan;
        setLimits({
          maxProducts: plan.max_products,
          maxOrders: plan.max_orders,
          maxStorageGb: plan.max_storage_gb,
          features: plan.features,
          canAddProducts: plan.max_products === -1 || usage.products < plan.max_products,
          canProcessOrders: plan.max_orders === -1 || usage.orders < plan.max_orders,
          hasAnalytics: plan.features.includes('Advanced Analytics'),
          hasInventoryManagement: plan.features.includes('Inventory Management'),
          hasPrioritySupport: plan.features.includes('Priority Support'),
        });
      }

      // Get usage stats
      await fetchUsageStats(user.id);
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUsageStats = async (userId: string) => {
    try {
      // Get product count
      const { count: productCount } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      // Get order count
      const { count: orderCount } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      // TODO: Calculate storage usage from uploaded images
      const storageUsed = 0;

      setUsage({
        products: productCount || 0,
        orders: orderCount || 0,
        storageUsed,
      });
    } catch (error) {
      console.error('Error fetching usage stats:', error);
    }
  };

  const checkProductLimit = (): boolean => {
    if (!limits) return true; // Allow if no subscription data
    return limits.maxProducts === -1 || usage.products < limits.maxProducts;
  };

  const checkOrderLimit = (): boolean => {
    if (!limits) return true;
    return limits.maxOrders === -1 || usage.orders < limits.maxOrders;
  };

  const checkStorageLimit = (additionalGb: number = 0): boolean => {
    if (!limits) return true;
    return (usage.storageUsed + additionalGb) <= limits.maxStorageGb;
  };

  const isTrialExpiringSoon = (daysThreshold: number = 3): boolean => {
    if (!subscription || subscription.status !== 'trial' || !subscription.trial_end) {
      return false;
    }
    const trialEnd = new Date(subscription.trial_end);
    const now = new Date();
    const daysLeft = Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysLeft <= daysThreshold;
  };

  const getTrialDaysLeft = (): number => {
    if (!subscription || subscription.status !== 'trial' || !subscription.trial_end) {
      return 0;
    }
    const trialEnd = new Date(subscription.trial_end);
    const now = new Date();
    return Math.max(0, Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
  };

  const isFeatureAvailable = (feature: string): boolean => {
    if (!limits) return false;
    return limits.features.includes(feature);
  };

  const getPlanName = (): string => {
    return subscription?.plan?.name || 'No Plan';
  };

  const isSubscriptionActive = (): boolean => {
    return subscription?.status === 'active' || subscription?.status === 'trial';
  };

  const refresh = () => {
    fetchSubscriptionData();
  };

  return {
    subscription,
    limits,
    usage,
    loading,
    checkProductLimit,
    checkOrderLimit,
    checkStorageLimit,
    isTrialExpiringSoon,
    getTrialDaysLeft,
    isFeatureAvailable,
    getPlanName,
    isSubscriptionActive,
    refresh,
  };
}
