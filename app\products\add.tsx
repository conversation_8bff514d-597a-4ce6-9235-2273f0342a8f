import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, ScrollView, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { supabase } from '../../lib/supabase';
import { SubscriptionGate } from '../../components/SubscriptionGate';

export default function AddProduct() {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [price, setPrice] = useState('');
  const [category, setCategory] = useState('');
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setImageUri(result.assets[0].uri);
    }
  };

  const addProduct = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a product name');
      return;
    }

    if (!price.trim() || isNaN(Number(price))) {
      Alert.alert('Error', 'Please enter a valid price');
      return;
    }

    setLoading(true);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        Alert.alert('Error', 'User not authenticated');
        return;
      }

      // Get store info
      const { data: store } = await supabase
        .from('stores')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (!store) {
        Alert.alert('Error', 'Store not found');
        return;
      }

      // Upload image to Supabase Storage if provided
      let imageUrl = null;
      if (imageUri) {
        try {
          // For demo purposes, we'll use the local URI
          // In production, you'd upload to Supabase Storage:
          // const { data: uploadData, error: uploadError } = await supabase.storage
          //   .from('product-images')
          //   .upload(`${store.id}/${Date.now()}.jpg`, imageFile);
          imageUrl = imageUri;
        } catch (uploadError) {
          console.error('Error uploading image:', uploadError);
        }
      }

      const { error } = await supabase
        .from('products')
        .insert({
          store_id: store.id,
          name: name.trim(),
          description: description.trim() || null,
          price: parseFloat(price),
          category: category.trim() || null,
          image_url: imageUrl,
          is_available: true,
        });

      if (error) {
        console.error('Error adding product:', error);
        Alert.alert('Error', 'Failed to add product');
        return;
      }

      Alert.alert('Success', 'Product added successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Error adding product:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-row items-center px-6 py-4 border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900 ml-4">Add Product</Text>
      </View>

      <SubscriptionGate checkLimit="products">
        <ScrollView className="flex-1 px-6 py-4">
          <View className="space-y-6">
            {/* Product Image */}
            <View>
              <Text className="text-gray-700 font-medium mb-3">Product Image</Text>
              <TouchableOpacity
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 items-center"
                onPress={pickImage}
              >
                {imageUri ? (
                  <Image source={{ uri: imageUri }} className="w-32 h-32 rounded-lg" />
                ) : (
                  <>
                    <Ionicons name="camera" size={48} color="#9ca3af" />
                    <Text className="text-gray-500 mt-2">Tap to add image</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>

            {/* Product Name */}
            <View>
              <Text className="text-gray-700 font-medium mb-2">Product Name *</Text>
              <TextInput
                className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
                placeholder="Enter product name"
                value={name}
                onChangeText={setName}
              />
            </View>

            {/* Description */}
            <View>
              <Text className="text-gray-700 font-medium mb-2">Description</Text>
              <TextInput
                className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
                placeholder="Describe your product..."
                value={description}
                onChangeText={setDescription}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            {/* Price */}
            <View>
              <Text className="text-gray-700 font-medium mb-2">Price (KSh) *</Text>
              <TextInput
                className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
                placeholder="0.00"
                value={price}
                onChangeText={setPrice}
                keyboardType="numeric"
              />
            </View>

            {/* Category */}
            <View>
              <Text className="text-gray-700 font-medium mb-2">Category</Text>
              <TextInput
                className="border border-gray-300 rounded-lg px-4 py-3 text-gray-900"
                placeholder="e.g., Electronics, Clothing, Food"
                value={category}
                onChangeText={setCategory}
              />
            </View>

            {/* Add Product Button */}
            <TouchableOpacity
              className={`bg-blue-600 rounded-lg py-4 mt-8 ${loading ? 'opacity-50' : ''}`}
              onPress={addProduct}
              disabled={loading}
            >
              <Text className="text-white text-center font-semibold text-lg">
                {loading ? 'Adding Product...' : 'Add Product'}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SubscriptionGate>
    </SafeAreaView>
  );
}
