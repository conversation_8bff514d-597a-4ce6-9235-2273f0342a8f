#!/usr/bin/env pwsh

# M-Duka Cleanup Script - Remove old Polar integration files
# This script removes all Polar-related files after migrating to Pesapal

Write-Host "🧹 Cleaning up old Polar integration files..." -ForegroundColor Yellow

$filesToRemove = @(
    "supabase\functions\polar-webhook\index.ts",
    "supabase\functions\polar-webhook\import_map.json", 
    "supabase\functions\polar-webhook\config.json",
    "hooks\usePolarSubscription.ts",
    "supabase-polar-schema.sql",
    "deploy-polar-webhook.ps1",
    "POLAR_WEBHOOK_SETUP.md",
    "test-webhook.ps1",
    "test-webhook.sh",
    ".env.polar"
)

$foldersToRemove = @(
    "supabase\functions\polar-webhook",
    "test-data"
)

# Remove files
foreach ($file in $filesToRemove) {
    $fullPath = Join-Path $PWD $file
    if (Test-Path $fullPath) {
        try {
            Remove-Item $fullPath -Force
            Write-Host "✅ Removed: $file" -ForegroundColor Green
        } catch {
            Write-Warning "Failed to remove: $file - $_"
        }
    } else {
        Write-Host "⚠️  File not found: $file" -ForegroundColor Gray
    }
}

# Remove folders
foreach ($folder in $foldersToRemove) {
    $fullPath = Join-Path $PWD $folder
    if (Test-Path $fullPath) {
        try {
            Remove-Item $fullPath -Recurse -Force
            Write-Host "✅ Removed folder: $folder" -ForegroundColor Green
        } catch {
            Write-Warning "Failed to remove folder: $folder - $_"
        }
    } else {
        Write-Host "⚠️  Folder not found: $folder" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "🎉 Cleanup completed!" -ForegroundColor Green
Write-Host "All Polar integration files have been removed." -ForegroundColor White
Write-Host ""
Write-Host "📋 Remaining tasks:" -ForegroundColor Yellow
Write-Host "1. Deploy the database schema to Supabase"
Write-Host "2. Deploy the Pesapal webhook function"
Write-Host "3. Configure webhook URL in Pesapal dashboard"
Write-Host "4. Test the complete integration"
