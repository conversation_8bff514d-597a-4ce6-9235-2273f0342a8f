import React from 'react';
import { View, Text, TouchableOpacity, Linking } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { isSupabaseConfigured } from '../lib/supabase';

export default function ConfigurationCheck({ children }: { children: React.ReactNode }) {
  if (isSupabaseConfigured()) {
    return <>{children}</>;
  }

  const openSupabaseGuide = () => {
    Linking.openURL('https://supabase.com/docs/guides/getting-started');
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-1 justify-center items-center px-6">
        <View className="bg-orange-100 rounded-full w-20 h-20 items-center justify-center mb-6">
          <Ionicons name="warning" size={40} color="#f59e0b" />
        </View>
        
        <Text className="text-2xl font-bold text-gray-900 mb-4 text-center">
          Supabase Not Configured
        </Text>
        
        <Text className="text-gray-600 text-center mb-8 leading-6">
          To use M-Duka, you need to set up a Supabase project and configure your environment variables.
        </Text>

        <View className="bg-gray-50 rounded-lg p-4 mb-8 w-full">
          <Text className="font-semibold text-gray-900 mb-3">Quick Setup Steps:</Text>
          <View className="space-y-2">
            <Text className="text-gray-700">1. Create a Supabase project at supabase.com</Text>
            <Text className="text-gray-700">2. Copy your project URL and anon key</Text>
            <Text className="text-gray-700">3. Update the .env file with your credentials</Text>
            <Text className="text-gray-700">4. Run the SQL schema in your Supabase dashboard</Text>
            <Text className="text-gray-700">5. Restart the app</Text>
          </View>
        </View>

        <TouchableOpacity
          className="bg-blue-600 rounded-lg px-6 py-3 mb-4"
          onPress={openSupabaseGuide}
        >
          <Text className="text-white font-semibold">Open Supabase Guide</Text>
        </TouchableOpacity>

        <View className="bg-blue-50 rounded-lg p-4 w-full">
          <Text className="text-blue-800 font-medium mb-2">Environment Variables Needed:</Text>
          <Text className="text-blue-700 text-sm font-mono">
            EXPO_PUBLIC_SUPABASE_URL=your_project_url{'\n'}
            EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}
