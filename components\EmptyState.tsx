import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface EmptyStateProps {
  icon: string;
  title: string;
  description: string;
  actionText?: string;
  onAction?: () => void;
}

export default function EmptyState({ 
  icon, 
  title, 
  description, 
  actionText, 
  onAction 
}: EmptyStateProps) {
  return (
    <View className="flex-1 justify-center items-center px-6">
      <Ionicons name={icon as any} size={64} color="#9ca3af" />
      <Text className="text-xl font-semibold text-gray-900 mt-4 text-center">{title}</Text>
      <Text className="text-gray-600 text-center mt-2">{description}</Text>
      {actionText && onAction && (
        <TouchableOpacity
          className="bg-blue-600 rounded-lg px-6 py-3 mt-6"
          onPress={onAction}
        >
          <Text className="text-white font-semibold">{actionText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}
