import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../../lib/supabase';
import { SubscriptionPlans } from '../../components/SubscriptionPlans';
import type { SubscriptionPlan, UserSubscription } from '../../lib/supabase';

export default function UpgradePlanScreen() {
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [availablePlans, setAvailablePlans] = useState<SubscriptionPlan[]>([]);
  const [selectedPlanId, setSelectedPlanId] = useState<string>('');
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [loading, setLoading] = useState(true);
  const [upgrading, setUpgrading] = useState(false);

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get current subscription
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans (*)
        `)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (subscription) {
        setCurrentSubscription(subscription);
      }

      // Get all available plans
      const { data: plans } = await supabase
        .from('subscription_plans')
        .select('*')
        .order('price_monthly');

      if (plans) {
        setAvailablePlans(plans);
        // Pre-select current plan or first plan
        if (subscription?.subscription_plans) {
          setSelectedPlanId(subscription.subscription_plans.id);
        } else if (plans.length > 0) {
          setSelectedPlanId(plans[0].id);
        }
      }
    } catch (error) {
      console.error('Error loading subscription data:', error);
      Alert.alert('Error', 'Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async () => {
    if (!selectedPlanId) {
      Alert.alert('Error', 'Please select a plan');
      return;
    }

    const selectedPlan = availablePlans.find(p => p.id === selectedPlanId);
    if (!selectedPlan) return;

    // Check if it's actually an upgrade/downgrade
    if (currentSubscription?.subscription_plans?.id === selectedPlanId) {
      Alert.alert('Info', 'You are already on this plan');
      return;
    }

    Alert.alert(
      'Confirm Plan Change',
      `Are you sure you want to change to the ${selectedPlan.name} plan? Your billing will be updated accordingly.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Confirm', onPress: performUpgrade },
      ]
    );
  };
  const performUpgrade = async () => {
    setUpgrading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get selected plan details
      const selectedPlan = availablePlans.find(p => p.id === selectedPlanId);
      if (!selectedPlan) throw new Error('Selected plan not found');

      // Get Pesapal payment URL based on plan name
      const getPesapalUrl = (planName: string) => {
        const productCodes = {
          'Basic': 'ff4ab78a-b437-4901-baa0-736b41efcf32',
          'Premium': '2186b965-2fb5-44da-952c-3dd6704270cc', 
          'Business': '77d32354-4ba7-4a21-9f62-04272852aca8'
        };
        
        const productCode = productCodes[planName as keyof typeof productCodes];
        return `https://store.pesapal.com/shop/svdp2z-omitechgroupcompanylimited?productCode=${productCode}`;
      };

      const paymentUrl = getPesapalUrl(selectedPlan.name);

      // Create a pending subscription record
      const { error: insertError } = await supabase
        .from('user_subscriptions')
        .insert({
          user_id: user.id,
          plan_id: selectedPlanId,
          status: 'pending',
          billing_cycle: billingCycle,
          pesapal_product_code: selectedPlan.name.toLowerCase() === 'basic' ? 'ff4ab78a-b437-4901-baa0-736b41efcf32' :
                               selectedPlan.name.toLowerCase() === 'premium' ? '2186b965-2fb5-44da-952c-3dd6704270cc' :
                               '77d32354-4ba7-4a21-9f62-04272852aca8',
          pesapal_merchant_reference: `${user.email}|${selectedPlan.name.toLowerCase() === 'basic' ? 'ff4ab78a-b437-4901-baa0-736b41efcf32' :
                                     selectedPlan.name.toLowerCase() === 'premium' ? '2186b965-2fb5-44da-952c-3dd6704270cc' :
                                     '77d32354-4ba7-4a21-9f62-04272852aca8'}`
        });

      if (insertError) {
        console.error('Error creating pending subscription:', insertError);
      }

      // Show payment redirection alert
      Alert.alert(
        'Redirecting to Payment',
        `You will be redirected to Pesapal to complete the payment for ${selectedPlan.name} plan ($${getSelectedPlanPrice()}).`,
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Continue to Payment', 
            onPress: () => {
              // TODO: Open Pesapal payment URL in browser
              // For now, show the URL to user
              Alert.alert(
                'Payment URL',
                `Please visit: ${paymentUrl}\n\nAfter payment, your subscription will be automatically activated.`,
                [{ text: 'OK' }]
              );
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error preparing upgrade:', error);
      Alert.alert('Error', 'Failed to prepare upgrade');
    } finally {
      setUpgrading(false);
    }
  };

  const getCurrentPlanPrice = () => {
    if (!currentSubscription?.subscription_plans) return 0;
    return billingCycle === 'monthly' 
      ? currentSubscription.subscription_plans.price_monthly
      : currentSubscription.subscription_plans.price_yearly;
  };

  const getSelectedPlanPrice = () => {
    const selectedPlan = availablePlans.find(p => p.id === selectedPlanId);
    if (!selectedPlan) return 0;
    return billingCycle === 'monthly' ? selectedPlan.price_monthly : selectedPlan.price_yearly;
  };

  const getPriceDifference = () => {
    return getSelectedPlanPrice() - getCurrentPlanPrice();
  };

  if (loading) {
    return (
      <View className="flex-1 bg-gray-50 justify-center items-center">
        <ActivityIndicator size="large" color="#3B82F6" />
        <Text className="text-gray-600 mt-4">Loading plans...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-white px-6 py-4 border-b border-gray-200">
        <View className="flex-row items-center">
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-xl font-semibold text-gray-900 ml-4">
            Change Plan
          </Text>
        </View>
      </View>

      <ScrollView className="flex-1">
        {/* Current Plan Info */}
        {currentSubscription && (
          <View className="bg-white mx-4 mt-4 p-4 rounded-lg border border-gray-200">
            <Text className="text-lg font-semibold text-gray-900 mb-2">
              Current Plan
            </Text>
            <View className="flex-row justify-between items-center">
              <View>
                <Text className="text-base font-medium text-gray-700">
                  {currentSubscription.subscription_plans?.name}
                </Text>
                <Text className="text-sm text-gray-500">
                  ${billingCycle === 'monthly' 
                    ? currentSubscription.subscription_plans?.price_monthly
                    : currentSubscription.subscription_plans?.price_yearly
                  }/{billingCycle === 'monthly' ? 'month' : 'year'}
                </Text>
              </View>
              {currentSubscription.trial_end_date && new Date(currentSubscription.trial_end_date) > new Date() && (
                <View className="bg-green-100 px-3 py-1 rounded-full">
                  <Text className="text-green-800 text-xs font-medium">
                    Free Trial
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Subscription Plans */}
        <View className="px-4 mt-6">
          <SubscriptionPlans
            plans={availablePlans}
            selectedPlanId={selectedPlanId}
            onPlanSelect={setSelectedPlanId}
            billingCycle={billingCycle}
            onBillingCycleChange={setBillingCycle}
            currentPlanId={currentSubscription?.subscription_plans?.id}
          />
        </View>

        {/* Price Comparison */}
        {currentSubscription && selectedPlanId !== currentSubscription.subscription_plans?.id && (
          <View className="bg-white mx-4 mt-6 p-4 rounded-lg border border-gray-200">
            <Text className="text-lg font-semibold text-gray-900 mb-3">
              Billing Summary
            </Text>
            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Current Plan</Text>
                <Text className="text-gray-900">
                  ${getCurrentPlanPrice()}/{billingCycle === 'monthly' ? 'mo' : 'yr'}
                </Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">New Plan</Text>
                <Text className="text-gray-900">
                  ${getSelectedPlanPrice()}/{billingCycle === 'monthly' ? 'mo' : 'yr'}
                </Text>
              </View>
              <View className="border-t border-gray-200 pt-2">
                <View className="flex-row justify-between">
                  <Text className="font-semibold text-gray-900">Difference</Text>
                  <Text className={`font-semibold ${getPriceDifference() >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {getPriceDifference() >= 0 ? '+' : ''}${getPriceDifference()}/{billingCycle === 'monthly' ? 'mo' : 'yr'}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Action Button */}
      <View className="bg-white px-6 py-4 border-t border-gray-200">
        <TouchableOpacity
          onPress={handleUpgrade}
          disabled={upgrading || selectedPlanId === currentSubscription?.subscription_plans?.id}
          className={`py-4 rounded-lg ${
            upgrading || selectedPlanId === currentSubscription?.subscription_plans?.id
              ? 'bg-gray-300'
              : 'bg-blue-600'
          }`}
        >
          {upgrading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text className="text-white text-center font-semibold text-base">
              {selectedPlanId === currentSubscription?.subscription_plans?.id 
                ? 'Current Plan' 
                : 'Change Plan'
              }
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}
