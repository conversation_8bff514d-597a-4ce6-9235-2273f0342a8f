import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function Marketing() {
  return (
    <View style={styles.container}>
      <View style={styles.pageHeader}>
        <Text style={styles.pageTitle}>Marketing</Text>
        <Text style={styles.pageSubtitle}>Promote your store and reach more customers.</Text>
      </View>
      <View style={styles.content}>
        <View style={styles.comingSoon}>
          <Ionicons name="megaphone-outline" size={64} color="#9ca3af" style={styles.icon} />
          <Text style={styles.comingSoonTitle}>Marketing Tools</Text>
          <Text style={styles.comingSoonText}>
            Powerful marketing tools to help you promote your store and reach more customers.
          </Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f9fafb' },
  pageHeader: { backgroundColor: 'white', paddingHorizontal: 24, paddingVertical: 20, borderBottomWidth: 1, borderBottomColor: '#e5e7eb' },
  pageTitle: { fontSize: 24, fontWeight: '700', color: '#1f2937', marginBottom: 4 },
  pageSubtitle: { fontSize: 14, color: '#6b7280' },
  content: { flex: 1, padding: 24 },
  comingSoon: { backgroundColor: 'white', borderRadius: 12, padding: 32, alignItems: 'center', shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 8, elevation: 4, borderWidth: 1, borderColor: '#f3f4f6' },
  icon: { marginBottom: 24 },
  comingSoonTitle: { fontSize: 20, fontWeight: '600', color: '#1f2937', marginBottom: 12, textAlign: 'center' },
  comingSoonText: { fontSize: 14, color: '#6b7280', textAlign: 'center', lineHeight: 20, marginBottom: 24 },
});
