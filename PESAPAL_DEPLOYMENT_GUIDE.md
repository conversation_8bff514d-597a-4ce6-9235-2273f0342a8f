# 🚀 M-<PERSON><PERSON> P<PERSON>apal Deployment Guide

## ✅ Step 1: Database Schema (COMPLETED NEXT)
1. Open Supabase SQL Editor: https://supabase.com/dashboard/project/vjwcnvdykoizguxoetgi/sql
2. Copy and paste the entire content from `supabase-pesapal-schema.sql`
3. Click "RUN" to execute the schema

## 📡 Step 2: Deploy Edge Function

### Option A: CLI Deployment (Requires Docker Desktop)
```powershell
# Install Docker Desktop first, then run:
supabase functions deploy pesapal-webhook --project-ref vjwcnvdykoizguxoetgi
```

### Option B: Manual Dashboard Deployment (Recommended)
1. Go to: https://supabase.com/dashboard/project/vjwcnvdykoizguxoetgi/functions
2. Click "Create a new function"
3. Function name: `pesapal-webhook`
4. Copy the content from `supabase/functions/pesapal-webhook/index.ts`
5. Paste it in the editor and deploy

## 🔧 Step 3: Set Environment Variables
1. Go to: https://supabase.com/dashboard/project/vjwcnvdykoizguxoetgi/settings/functions
2. Add environment variable:
   - Name: `PESAPAL_WEBHOOK_SECRET`
   - Value: [Your Pesapal webhook secret from Pesapal dashboard]

## 🔗 Step 4: Configure Pesapal Webhook
1. Your webhook URL will be:
   ```
   https://vjwcnvdykoizguxoetgi.supabase.co/functions/v1/pesapal-webhook
   ```
2. Add this URL to your Pesapal merchant dashboard
3. Make sure to set the webhook secret in both Pesapal and Supabase

## 🧪 Step 5: Test the Integration
1. Make a test payment using one of the Pesapal product URLs
2. Check the `webhook_events` table in your Supabase database
3. Verify that subscriptions are being created/updated

## 📋 Verification Checklist
- [ ] Database schema deployed successfully
- [ ] Edge function deployed and running
- [ ] Environment variable `PESAPAL_WEBHOOK_SECRET` set
- [ ] Pesapal webhook URL configured
- [ ] Test payment processed successfully
- [ ] Subscription data appears in database

## 🆘 Troubleshooting
- Check Supabase Edge Function logs for any errors
- Verify webhook signatures are matching
- Ensure Pesapal product codes match your database entries
- Check that RLS policies allow proper data access

## 📞 Next Steps After Deployment
1. Update your frontend app to use the new Pesapal integration
2. Test the complete subscription flow
3. Remove old Polar-related code and files
4. Update your app store listings to mention Pesapal payments
