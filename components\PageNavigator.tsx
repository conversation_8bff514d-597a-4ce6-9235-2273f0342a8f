import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

interface PageInfo {
  name: string;
  route: string;
  description: string;
  category: string;
  icon: string;
}

const pages: PageInfo[] = [
  // Authentication
  { name: 'Login', route: '/auth/login', description: 'User login screen', category: 'Auth', icon: 'log-in' },
  { name: 'Signup', route: '/auth/signup', description: 'User registration', category: 'Auth', icon: 'person-add' },
  
  // Onboarding
  { name: 'Splash', route: '/splash', description: 'App loading screen', category: 'Setup', icon: 'flash' },
  { name: 'Onboarding', route: '/onboarding', description: 'Welcome & setup', category: 'Setup', icon: 'rocket' },
  { name: 'Organization Setup', route: '/organization-setup', description: 'Store configuration', category: 'Setup', icon: 'business' },
  
  // Main Tabs
  { name: 'Dashboard', route: '/(tabs)/', description: 'Main dashboard', category: 'Main', icon: 'home' },
  { name: 'Products', route: '/(tabs)/products', description: 'Product management', category: 'Main', icon: 'cube' },
  { name: 'Orders', route: '/(tabs)/orders', description: 'Order management', category: 'Main', icon: 'receipt' },
  { name: 'Store', route: '/(tabs)/store', description: 'Store overview', category: 'Main', icon: 'storefront' },
  { name: 'Settings', route: '/(tabs)/settings', description: 'App settings', category: 'Main', icon: 'settings' },
  
  // Product Management
  { name: 'Add Product', route: '/products/add', description: 'Add new product', category: 'Products', icon: 'add-circle' },
  
  // Settings
  { name: 'Store Info', route: '/settings/store-info', description: 'Store information', category: 'Settings', icon: 'information-circle' },
  { name: 'WhatsApp', route: '/settings/whatsapp', description: 'WhatsApp integration', category: 'Settings', icon: 'logo-whatsapp' },
];

const categories = ['All', 'Auth', 'Setup', 'Main', 'Products', 'Settings'];

export default function PageNavigator() {
  const [isVisible, setIsVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('All');

  const filteredPages = selectedCategory === 'All' 
    ? pages 
    : pages.filter(page => page.category === selectedCategory);

  const navigateToPage = (route: string) => {
    setIsVisible(false);
    try {
      router.push(route as any);
    } catch (error) {
      console.error('Navigation error:', error);
      alert(`Navigation to ${route} failed. This page might not exist or have issues.`);
    }
  };

  return (
    <>
      {/* Floating Action Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => setIsVisible(true)}
        activeOpacity={0.8}
      >
        <Ionicons name="apps" size={24} color="white" />
      </TouchableOpacity>

      {/* Navigation Modal */}
      <Modal
        visible={isVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsVisible(false)}
      >
        <SafeAreaView style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>🗺️ Page Navigator</Text>
            <TouchableOpacity
              onPress={() => setIsVisible(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Category Filter */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryContainer}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryButton,
                  selectedCategory === category && styles.categoryButtonActive
                ]}
                onPress={() => setSelectedCategory(category)}
              >
                <Text style={[
                  styles.categoryText,
                  selectedCategory === category && styles.categoryTextActive
                ]}>
                  {category}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Pages List */}
          <ScrollView style={styles.pagesList}>
            {filteredPages.map((page, index) => (
              <TouchableOpacity
                key={index}
                style={styles.pageItem}
                onPress={() => navigateToPage(page.route)}
                activeOpacity={0.7}
              >
                <View style={styles.pageIcon}>
                  <Ionicons name={page.icon as any} size={20} color="#667eea" />
                </View>
                <View style={styles.pageInfo}>
                  <Text style={styles.pageName}>{page.name}</Text>
                  <Text style={styles.pageDescription}>{page.description}</Text>
                  <Text style={styles.pageRoute}>{page.route}</Text>
                </View>
                <Ionicons name="chevron-forward" size={16} color="#ccc" />
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              📱 Total Pages: {filteredPages.length} | 🔧 Developer Navigation Tool
            </Text>
          </View>
        </SafeAreaView>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1000,
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  categoryContainer: {
    backgroundColor: 'white',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  categoryButtonActive: {
    backgroundColor: '#667eea',
    borderColor: '#667eea',
  },
  categoryText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  categoryTextActive: {
    color: 'white',
  },
  pagesList: {
    flex: 1,
    padding: 16,
  },
  pageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  pageIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f4ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  pageInfo: {
    flex: 1,
  },
  pageName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  pageDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  pageRoute: {
    fontSize: 12,
    color: '#999',
    fontFamily: 'monospace',
  },
  footer: {
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  footerText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});
