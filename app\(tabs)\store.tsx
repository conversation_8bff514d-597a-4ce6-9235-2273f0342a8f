import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Share, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { supabase, Store } from '../../lib/supabase';

export default function StoreScreen() {
  const [store, setStore] = useState<Store | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStore();
  }, []);

  const loadStore = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: storeData, error } = await supabase
        .from('stores')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error loading store:', error);
        return;
      }

      setStore(storeData);
    } catch (error) {
      console.error('Error loading store:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleStoreStatus = async () => {
    if (!store) return;

    const { error } = await supabase
      .from('stores')
      .update({
        is_active: !store.is_active,
        updated_at: new Date().toISOString()
      })
      .eq('id', store.id);

    if (error) {
      Alert.alert('Error', 'Failed to update store status');
    } else {
      setStore({ ...store, is_active: !store.is_active });
    }
  };

  const shareStore = async () => {
    if (!store) return;

    const storeUrl = `${process.env.EXPO_PUBLIC_BASE_URL || 'https://mduka.app'}/${store.slug}`;
    try {
      await Share.share({
        message: `Check out my store "${store.name}" at ${storeUrl}`,
        url: storeUrl,
        title: store.name,
      });
    } catch (error) {
      console.error('Error sharing store:', error);
    }
  };

  const copyStoreLink = async () => {
    if (!store) return;

    const storeUrl = `${process.env.EXPO_PUBLIC_BASE_URL || 'https://mduka.app'}/${store.slug}`;
    // Note: In a real app, you'd use Clipboard API
    Alert.alert('Store Link', storeUrl, [
      { text: 'OK' }
    ]);
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <View className="flex-1 justify-center items-center">
          <Text className="text-gray-600">Loading store...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!store) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <View className="flex-1 justify-center items-center px-6">
          <Ionicons name="storefront-outline" size={64} color="#9ca3af" />
          <Text className="text-xl font-semibold text-gray-900 mt-4">No Store Found</Text>
          <Text className="text-gray-600 text-center mt-2">
            Please complete the onboarding process to set up your store
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1">
        <View className="px-6 py-4">
          <Text className="text-2xl font-bold text-gray-900 mb-6">My Store</Text>

          {/* Store Status */}
          <View className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 mb-4">
            <View className="flex-row items-center justify-between">
              <View>
                <Text className="text-lg font-semibold text-gray-900">{store.name}</Text>
                <Text className="text-gray-600 mt-1">mduka.app/{store.slug}</Text>
              </View>
              <View className="flex-row items-center">
                <View className={`w-3 h-3 rounded-full mr-2 ${store.is_active ? 'bg-green-500' : 'bg-red-500'}`} />
                <Text className={`font-medium ${store.is_active ? 'text-green-600' : 'text-red-600'}`}>
                  {store.is_active ? 'Open' : 'Closed'}
                </Text>
              </View>
            </View>

            {store.description && (
              <Text className="text-gray-600 mt-3">{store.description}</Text>
            )}

            <TouchableOpacity
              className={`mt-4 rounded-lg py-3 ${store.is_active ? 'bg-red-600' : 'bg-green-600'}`}
              onPress={toggleStoreStatus}
            >
              <Text className="text-white text-center font-semibold">
                {store.is_active ? 'Close Store' : 'Open Store'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Store Actions */}
          <View className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 mb-4">
            <Text className="text-lg font-semibold text-gray-900 mb-4">Share Your Store</Text>
            
            <View className="space-y-3">
              <TouchableOpacity
                className="flex-row items-center p-3 bg-blue-50 rounded-lg"
                onPress={shareStore}
              >
                <Ionicons name="share" size={24} color="#3b82f6" />
                <Text className="text-blue-600 font-medium ml-3">Share Store Link</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="flex-row items-center p-3 bg-gray-50 rounded-lg"
                onPress={copyStoreLink}
              >
                <Ionicons name="copy" size={24} color="#6b7280" />
                <Text className="text-gray-600 font-medium ml-3">Copy Store Link</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Store Info */}
          <View className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 mb-4">
            <Text className="text-lg font-semibold text-gray-900 mb-4">Store Information</Text>
            
            <View className="space-y-3">
              <View className="flex-row">
                <Text className="text-gray-600 w-24">Name:</Text>
                <Text className="text-gray-900 flex-1">{store.name}</Text>
              </View>
              
              <View className="flex-row">
                <Text className="text-gray-600 w-24">URL:</Text>
                <Text className="text-blue-600 flex-1">mduka.app/{store.slug}</Text>
              </View>
              
              {store.whatsapp_number && (
                <View className="flex-row">
                  <Text className="text-gray-600 w-24">WhatsApp:</Text>
                  <Text className="text-gray-900 flex-1">{store.whatsapp_number}</Text>
                </View>
              )}
              
              {store.business_hours && (
                <View className="flex-row">
                  <Text className="text-gray-600 w-24">Hours:</Text>
                  <Text className="text-gray-900 flex-1">{store.business_hours}</Text>
                </View>
              )}
              
              <View className="flex-row">
                <Text className="text-gray-600 w-24">Created:</Text>
                <Text className="text-gray-900 flex-1">
                  {new Date(store.created_at).toLocaleDateString()}
                </Text>
              </View>
            </View>
          </View>

          {/* Payment Instructions */}
          {store.payment_instructions && (
            <View className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
              <Text className="text-lg font-semibold text-gray-900 mb-3">Payment Instructions</Text>
              <Text className="text-gray-600">{store.payment_instructions}</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
