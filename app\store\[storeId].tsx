import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, Linking, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { supabase, Store, Product } from '../../lib/supabase';

export default function PublicStore() {
  const { storeId } = useLocalSearchParams<{ storeId: string }>();
  const [store, setStore] = useState<Store | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [cart, setCart] = useState<{ [key: string]: number }>({});

  useEffect(() => {
    if (storeId) {
      loadStoreData();
    }
  }, [storeId]);

  const loadStoreData = async () => {
    try {
      // Load store info
      const { data: storeData, error: storeError } = await supabase
        .from('stores')
        .select('*')
        .eq('slug', storeId)
        .eq('is_active', true)
        .single();

      if (storeError || !storeData) {
        Alert.alert('Error', 'Store not found or is currently closed');
        return;
      }

      setStore(storeData);

      // Load products
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select('*')
        .eq('store_id', storeData.id)
        .eq('is_available', true)
        .order('created_at', { ascending: false });

      if (productsError) {
        console.error('Error loading products:', productsError);
        return;
      }

      setProducts(productsData || []);
    } catch (error) {
      console.error('Error loading store data:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = (productId: string) => {
    setCart(prev => ({
      ...prev,
      [productId]: (prev[productId] || 0) + 1
    }));
  };

  const removeFromCart = (productId: string) => {
    setCart(prev => {
      const newCart = { ...prev };
      if (newCart[productId] > 1) {
        newCart[productId]--;
      } else {
        delete newCart[productId];
      }
      return newCart;
    });
  };

  const getCartTotal = () => {
    return Object.entries(cart).reduce((total, [productId, quantity]) => {
      const product = products.find(p => p.id === productId);
      return total + (product ? product.price * quantity : 0);
    }, 0);
  };

  const generateWhatsAppMessage = () => {
    if (!store) return '';

    const cartItems = Object.entries(cart).map(([productId, quantity]) => {
      const product = products.find(p => p.id === productId);
      return product ? `${quantity}x ${product.name} - KSh ${product.price.toLocaleString()}` : '';
    }).filter(Boolean);

    const total = getCartTotal();
    
    return `Hello! I'd like to place an order from ${store.name}:

${cartItems.join('\n')}

Total: KSh ${total.toLocaleString()}

Please confirm my order. Thank you!`;
  };

  const orderViaWhatsApp = () => {
    if (!store?.whatsapp_number) {
      Alert.alert('Error', 'WhatsApp number not available for this store');
      return;
    }

    if (Object.keys(cart).length === 0) {
      Alert.alert('Error', 'Please add items to your cart first');
      return;
    }

    const message = generateWhatsAppMessage();
    const whatsappUrl = `whatsapp://send?phone=${store.whatsapp_number}&text=${encodeURIComponent(message)}`;
    
    Linking.openURL(whatsappUrl).catch(() => {
      Alert.alert('Error', 'WhatsApp is not installed on this device');
    });
  };

  const ProductCard = ({ product }: { product: Product }) => {
    const quantity = cart[product.id] || 0;

    return (
      <View className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 mb-4">
        <View className="flex-row">
          {product.image_url ? (
            <Image
              source={{ uri: product.image_url }}
              className="w-20 h-20 rounded-lg mr-4"
              resizeMode="cover"
            />
          ) : (
            <View className="w-20 h-20 bg-gray-200 rounded-lg mr-4 items-center justify-center">
              <Ionicons name="image" size={24} color="#9ca3af" />
            </View>
          )}
          
          <View className="flex-1">
            <Text className="font-semibold text-gray-900 text-lg">{product.name}</Text>
            {product.description && (
              <Text className="text-gray-600 text-sm mt-1" numberOfLines={2}>
                {product.description}
              </Text>
            )}
            <Text className="text-blue-600 font-bold text-lg mt-2">
              KSh {product.price.toLocaleString()}
            </Text>
            {product.category && (
              <Text className="text-gray-500 text-xs mt-1">{product.category}</Text>
            )}
          </View>
        </View>
        
        <View className="flex-row items-center justify-between mt-4">
          <View className="flex-row items-center">
            <TouchableOpacity
              className="w-8 h-8 bg-gray-200 rounded-full items-center justify-center"
              onPress={() => removeFromCart(product.id)}
              disabled={quantity === 0}
            >
              <Ionicons name="remove" size={16} color="#374151" />
            </TouchableOpacity>
            <Text className="mx-4 font-semibold text-lg">{quantity}</Text>
            <TouchableOpacity
              className="w-8 h-8 bg-blue-600 rounded-full items-center justify-center"
              onPress={() => addToCart(product.id)}
            >
              <Ionicons name="add" size={16} color="white" />
            </TouchableOpacity>
          </View>
          
          {quantity > 0 && (
            <Text className="text-blue-600 font-semibold">
              KSh {(product.price * quantity).toLocaleString()}
            </Text>
          )}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <View className="flex-1 justify-center items-center">
          <Text className="text-gray-600">Loading store...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!store) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <View className="flex-1 justify-center items-center px-6">
          <Ionicons name="storefront-outline" size={64} color="#9ca3af" />
          <Text className="text-xl font-semibold text-gray-900 mt-4">Store Not Found</Text>
          <Text className="text-gray-600 text-center mt-2">
            This store is not available or has been closed
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const cartItemCount = Object.values(cart).reduce((sum, qty) => sum + qty, 0);

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="flex-1">
        {/* Store Header */}
        <View className="bg-white px-6 py-4 border-b border-gray-200">
          <Text className="text-2xl font-bold text-gray-900">{store.name}</Text>
          {store.description && (
            <Text className="text-gray-600 mt-1">{store.description}</Text>
          )}
          {store.business_hours && (
            <Text className="text-gray-500 text-sm mt-2">Hours: {store.business_hours}</Text>
          )}
        </View>

        {/* Products */}
        <ScrollView className="flex-1 px-6 py-4">
          {products.length === 0 ? (
            <View className="flex-1 justify-center items-center py-20">
              <Ionicons name="cube-outline" size={64} color="#9ca3af" />
              <Text className="text-xl font-semibold text-gray-900 mt-4">No Products Available</Text>
              <Text className="text-gray-600 text-center mt-2">
                This store doesn't have any products yet
              </Text>
            </View>
          ) : (
            products.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))
          )}
        </ScrollView>

        {/* Cart Summary & Order Button */}
        {cartItemCount > 0 && (
          <View className="bg-white px-6 py-4 border-t border-gray-200">
            <View className="flex-row items-center justify-between mb-4">
              <Text className="text-lg font-semibold text-gray-900">
                {cartItemCount} item{cartItemCount > 1 ? 's' : ''} in cart
              </Text>
              <Text className="text-xl font-bold text-blue-600">
                KSh {getCartTotal().toLocaleString()}
              </Text>
            </View>
            
            <TouchableOpacity
              className="bg-green-600 rounded-lg py-4 flex-row items-center justify-center"
              onPress={orderViaWhatsApp}
            >
              <Ionicons name="logo-whatsapp" size={24} color="white" />
              <Text className="text-white font-semibold text-lg ml-2">
                Order via WhatsApp
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}
