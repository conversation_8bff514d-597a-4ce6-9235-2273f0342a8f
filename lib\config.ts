// Production Configuration for M-Duka
import Constants from 'expo-constants';

// Environment detection
export const isDevelopment = __DEV__;
export const isProduction = !__DEV__;

// App Configuration
export const APP_CONFIG = {
  name: process.env.EXPO_PUBLIC_APP_NAME || 'M-Duka',
  version: Constants.expoConfig?.version || '1.0.0',
  slug: Constants.expoConfig?.slug || 'm-duka',
  baseUrl: process.env.EXPO_PUBLIC_BASE_URL || 'https://mduka.vercel.app',
  
  // API Configuration
  api: {
    timeout: isProduction ? 10000 : 30000, // 10s prod, 30s dev
    retries: isProduction ? 3 : 1,
  },
  
  // Feature Flags
  features: {
    analytics: isProduction,
    errorReporting: isProduction,
    debugMode: isDevelopment,
    performanceMonitoring: isProduction,
  },
  
  // Cache Configuration
  cache: {
    ttl: isProduction ? 300000 : 60000, // 5min prod, 1min dev
    maxSize: isProduction ? 50 : 10, // MB
  },
  
  // Security Configuration
  security: {
    enforceHttps: isProduction,
    enableCSP: isProduction,
    enableHSTS: isProduction,
  }
};

// Supabase Configuration
export const SUPABASE_CONFIG = {
  url: process.env.EXPO_PUBLIC_SUPABASE_URL!,
  anonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!,
  
  // Connection options
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce' as const,
  },
  
  // Database options
  db: {
    schema: 'public',
  },
  
  // Real-time options
  realtime: {
    params: {
      eventsPerSecond: isProduction ? 10 : 100,
    },
  },
  
  // Global options
  global: {
    headers: {
      'x-application-name': APP_CONFIG.name,
      'x-application-version': APP_CONFIG.version,
    },
  },
};

// Validation
export const validateConfig = () => {
  const errors: string[] = [];
  
  if (!SUPABASE_CONFIG.url || SUPABASE_CONFIG.url === 'https://placeholder.supabase.co') {
    errors.push('EXPO_PUBLIC_SUPABASE_URL is not configured');
  }
  
  if (!SUPABASE_CONFIG.anonKey || SUPABASE_CONFIG.anonKey === 'placeholder-key') {
    errors.push('EXPO_PUBLIC_SUPABASE_ANON_KEY is not configured');
  }
  
  if (!SUPABASE_CONFIG.url.includes('supabase.co')) {
    errors.push('Invalid Supabase URL format');
  }
  
  if (isProduction && !APP_CONFIG.baseUrl.startsWith('https://')) {
    errors.push('Production base URL must use HTTPS');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Performance monitoring
export const PERFORMANCE_CONFIG = {
  // Bundle size monitoring
  bundleAnalysis: isProduction,
  
  // Runtime performance
  enableProfiling: isDevelopment,
  
  // Network monitoring
  networkLogging: isDevelopment,
  
  // Memory monitoring
  memoryProfiling: isDevelopment,
};

// Analytics Configuration
export const ANALYTICS_CONFIG = {
  enabled: isProduction,
  
  // Events to track
  events: {
    userSignup: 'user_signup',
    userLogin: 'user_login',
    storeCreated: 'store_created',
    productAdded: 'product_added',
    orderPlaced: 'order_placed',
    subscriptionUpgrade: 'subscription_upgrade',
  },
  
  // User properties
  userProperties: {
    subscriptionTier: 'subscription_tier',
    storeCategory: 'store_category',
    productsCount: 'products_count',
  },
};

// Error Reporting Configuration
export const ERROR_CONFIG = {
  enabled: isProduction,
  
  // Error levels
  levels: {
    fatal: 'fatal',
    error: 'error',
    warning: 'warning',
    info: 'info',
  },
  
  // Ignored errors
  ignoredErrors: [
    'Network request failed',
    'AbortError',
    'Non-Error promise rejection captured',
  ],
  
  // User context
  includeUserContext: true,
  includeDeviceContext: true,
  includeBreadcrumbs: true,
};

// SEO Configuration
export const SEO_CONFIG = {
  title: 'M-Duka - Create Your Online Store',
  description: 'Build and manage your online store with M-Duka. Easy setup, mobile-first design, and powerful features for small businesses.',
  keywords: 'online store, ecommerce, small business, mobile store, Tanzania, Kenya, Africa',
  author: 'M-Duka Team',
  
  // Open Graph
  og: {
    type: 'website',
    siteName: 'M-Duka',
    image: `${APP_CONFIG.baseUrl}/assets/og-image.png`,
    imageAlt: 'M-Duka - Online Store Builder',
  },
  
  // Twitter Card
  twitter: {
    card: 'summary_large_image',
    site: '@mduka_app',
    creator: '@mduka_app',
  },
};

// Export configuration validation result
export const CONFIG_VALIDATION = validateConfig();
