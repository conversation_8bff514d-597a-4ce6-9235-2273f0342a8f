# Environment Variables for Polar Webhook Integration

# Supabase Configuration (get these from your Supabase dashboard)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Polar Configuration (get these from your Polar dashboard)
POLAR_WEBHOOK_SECRET=your-webhook-secret-from-polar
POLAR_API_TOKEN=your-polar-api-token

# For testing webhooks locally
# Generate a test secret or use: openssl rand -hex 32
LOCAL_WEBHOOK_SECRET=your-local-webhook-secret-for-testing

# Optional: For additional logging/monitoring
WEBHOOK_DEBUG=true
LOG_LEVEL=info
